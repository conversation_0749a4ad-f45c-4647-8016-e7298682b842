# AI Coach Data Organization

This folder contains all the data for the AI Coach application, organized for better maintainability and scalability.

## Folder Structure

### `/curriculum/`
- Core curriculum and course progression data
- Module definitions and learning paths
- Academic field specializations

### `/techniques/`
- Individual learning techniques and methods
- Organized by the 3 pillars: Enablers, Retrieval, Encoding
- Implementation guides and examples

### `/coaching_brain/`
- Strategic coaching intelligence
- Scenario handlers and response patterns
- User archetype strategies

### `/assessments/`
- Assessment frameworks and criteria
- Progress tracking templates
- Skill evaluation rubrics

### `/user_profiles/`
- Academic field profiles (computational linguistics, phonetics, etc.)
- User archetype definitions
- Personalization templates

### `/app_guidance/`
- Application usage instructions
- Command references
- Feature explanations

## Migration Notes

This new structure replaces the previous scattered organization:
- `Coach_Brain/` → `data/coaching_brain/`
- `knowledge_data/` → Multiple organized subfolders
- Better separation of concerns
- Clearer data relationships
