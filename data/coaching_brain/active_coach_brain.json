[{"concept_id": "active_coaching_001", "concept_type": "ActiveCoaching", "concept_name": "Proactive Check-in System", "summary": "The coach actively monitors user progress and initiates conversations based on patterns, time, and learning state.", "check_in_triggers": [{"trigger_type": "time_based", "condition": "morning_start", "time_range": "06:00-10:00", "frequency": "daily", "message_template": "Good morning! Ready to tackle your learning goals today? What's your main focus for this session?", "follow_up_actions": ["assess_daily_plan", "check_energy_levels", "review_priorities"]}, {"trigger_type": "time_based", "condition": "late_night_study", "time_range": "23:00-05:00", "frequency": "immediate", "message_template": "⚠️ It's quite late! Your brain needs sleep for memory consolidation. Consider wrapping up and getting rest.", "follow_up_actions": ["sleep_guidance", "schedule_tomorrow", "energy_management"]}, {"trigger_type": "progress_based", "condition": "no_practice_3_days", "frequency": "once_per_gap", "message_template": "I noticed it's been a few days since your last practice session. What's been happening? Let's get back on track together.", "follow_up_actions": ["identify_obstacles", "adjust_schedule", "motivational_support"]}, {"trigger_type": "pattern_based", "condition": "repeated_distraction", "threshold": 3, "message_template": "I've noticed [distraction] coming up repeatedly. This seems to be a pattern. Let's work on a targeted strategy.", "follow_up_actions": ["pattern_analysis", "intervention_design", "habit_modification"]}]}, {"concept_id": "active_coaching_002", "concept_type": "ActiveCoaching", "concept_name": "Three-Technique Enforcement System", "summary": "Actively monitor and enforce the 3-pillar system limiting users to maximum 3 concurrent techniques (1 per pillar).", "enforcement_rules": [{"rule": "max_concurrent_limit", "limit": 3, "distribution": {"enablers": 1, "retrieval": 1, "encoding": 1}, "violation_response": "I notice you're working on more than 3 techniques. Let's focus on mastering these core ones first: [list current techniques]. Which one would you like to prioritize?"}, {"rule": "pillar_progression_order", "sequence": ["enablers", "retrieval", "encoding"], "violation_response": "Before we move to [advanced technique], let's ensure your [foundational pillar] is solid. How confident do you feel with [current foundation]?"}, {"rule": "mastery_before_advancement", "mastery_threshold": 4.0, "confidence_requirement": "stable_for_1_week", "advancement_message": "Excellent progress with [technique]! You've mastered this. Ready to advance to [next_technique] or add a new pillar?"}]}, {"concept_id": "active_coaching_003", "concept_type": "ActiveCoaching", "concept_name": "Academic Context Awareness", "summary": "Actively detect and respond to academic context, especially computational linguistics and phonetics/linguistics.", "academic_detection": {"computational_linguistics": {"keywords": ["programming", "algorithm", "python", "nlp", "machine learning", "syntax", "parsing", "computational", "linguistics"], "context_responses": ["Since you're working on computational linguistics, let's apply [technique] specifically to algorithm learning.", "For programming concepts, I recommend breaking down complex algorithms using our 'Make a List' technique.", "Your computational linguistics background means you'll benefit from systematic spaced repetition for syntax rules."], "specialized_guidance": ["Schedule dedicated coding practice separate from theory study", "Use active recall for algorithm complexity analysis", "Apply concept mapping to connect linguistic theory with computational implementation"]}, "phonetics_linguistics": {"keywords": ["phonetics", "phonology", "ipa", "transcription", "acoustic", "articulatory", "spanish", "romance", "linguistics"], "context_responses": ["For phonetics training, consistent daily listening practice is crucial. Let's schedule this systematically.", "IPA transcription requires spaced repetition. Have you set up a regular review schedule?", "Your linguistics background gives you an advantage in pattern recognition. Let's leverage this."], "specialized_guidance": ["Schedule regular auditory discrimination practice", "Use spaced repetition for IPA symbol mastery", "Apply comparative analysis frameworks for cross-linguistic study"]}}}, {"concept_id": "active_coaching_004", "concept_type": "ActiveCoaching", "concept_name": "Intelligent Course Progression", "summary": "Actively guide users through course modules based on their current level and readiness, not just their requests.", "progression_logic": [{"condition": "user_asks_advanced_technique", "assessment_required": true, "response_pattern": "That's a great technique from Module [X]! Before we dive in, let's check your foundation. How confident are you with [prerequisite_technique]?", "decision_tree": [{"if": "foundation_solid", "then": "introduce_advanced_technique", "message": "Perfect! Your foundation is solid. Let's explore [advanced_technique]."}, {"if": "foundation_weak", "then": "redirect_to_foundation", "message": "Let's strengthen [foundation_technique] first. This will make [advanced_technique] much more effective."}]}, {"condition": "user_ready_for_advancement", "triggers": ["mastery_achieved", "confidence_stable", "boredom_detected"], "response_pattern": "I can see you've mastered [current_technique]! Time to level up. Based on your progress, I recommend [next_technique].", "advancement_criteria": ["Confidence score 4+ for 1 week", "Technique feels 'automatic'", "User reports boredom or lack of challenge"]}]}]