[{"concept_id": "ineffective_001", "concept_name": "Passive Learning (Re-reading, Re-writing, Re-listening)", "summary": "An ineffective set of techniques involving simply reviewing material without specific cognitive focus. These methods do not activate effective learning pathways, leading to poor retention, and are often time-consuming and demotivating.", "ics_phase": "Retrieval", "instructions": ["Avoid simply listening to, reading, or re-writing notes without a specific retrieval goal.", "Do not attempt to learn by just mentally repeating sentences or passively 'trying to understand'."], "keywords": ["passive learning", "re-reading", "re-writing", "re-listening", "ineffective", "useless technique", "memorization"], "additional_context": "These techniques offer so little benefit that they should be avoided in all circumstances. Active retrieval methods are always a better alternative for the same amount of time."}, {"concept_id": "ineffective_002", "concept_name": "REBIM (Repetitive Execution Beyond Initial Mastery)", "summary": "An ineffective technique of repetitively practicing skills or solving problems that you have already mastered. The benefit is negligible compared to tackling more challenging or varied material.", "ics_phase": "Retrieval", "instructions": ["Once you have mastered a skill or type of problem, avoid repeating it without variation or added complexity.", "Do not repeatedly solve the same difficulty of math questions or code the same simple scripts after proficiency is achieved."], "keywords": ["REBIM", "repetitive execution", "over-practicing", "mastery", "useless technique", "ineffective", "justin sung"], "additional_context": "Coined by Dr<PERSON> <PERSON>, REBIM is a common pitfall in procedural learning. The key is to progress to more complex applications (Integrative/Applied Execution) or introduce new challenges and variables."}, {"concept_id": "ineffective_003", "concept_name": "Passive Practice Questions", "summary": "An ineffective method of using practice questions where one immediately checks the answer, answers only mentally, or assumes they 'could solve that' without actually doing it. This creates an illusion of competence and fails to identify true knowledge gaps.", "ics_phase": "Retrieval", "instructions": ["Avoid looking at the answer immediately after reading a practice question.", "Do not just think through the answer mentally; always generate a full, concrete answer.", "Never assume you can solve a problem just because it looks familiar."], "keywords": ["passive practice", "practice questions", "illusion of competence", "ineffective", "useless technique", "familiarity"], "additional_context": "This method wastes valuable practice questions and consistently fails to identify knowledge gaps because the brain tends to overestimate ability and underestimate challenges. Use active practice question methods instead."}, {"concept_id": "ineffective_004", "concept_name": "Undirected Group Discussion", "summary": "A form of group study that lacks a specific goal or structure. It is an inefficient use of time compared to structured, active learning techniques.", "ics_phase": "Retrieval", "instructions": ["Avoid group study sessions that do not have a clear, explicit goal.", "Replace undirected discussion with more effective alternatives like the 'Advanced Group Method' or 'Evaluative Peer Discussion'."], "keywords": ["group discussion", "undirected", "group study", "ineffective", "useless technique"], "additional_context": "While group discussion can be useful, most of it wastes time. Effective group study requires a clear purpose, such as critiquing concepts, solving specific problems, or testing each other."}, {"concept_id": "encoding_001", "concept_name": "Declarative Knowledge", "summary": "Declarative knowledge is information based on static facts, concepts, and logic. It is the 'what' of learning and involves encoding content material like theories, definitions, and principles.", "ics_phase": "Encoding", "instructions": ["Identify the declarative components of your subject, such as facts, theories, concepts, and logical rules.", "Use declarative-focused techniques to learn and retrieve this type of information."], "keywords": ["declarative knowledge", "conceptual knowledge", "descriptive knowledge", "facts", "theory", "concepts"], "additional_context": "Examples of declarative-heavy subjects include science, history, law, and economic theory. The information itself is static (e.g., kidney functions don't change), though your understanding of it can deepen."}, {"concept_id": "encoding_002", "concept_name": "Procedural Knowledge", "summary": "Procedural knowledge is the 'how-to' knowledge of performing skills, actions, and processes. It is dynamic, skill-based, and improved through practice and hands-on experience.", "ics_phase": "Encoding", "instructions": ["Identify the procedural components of your subject, such as solving problems, speaking a language, or applying a technique.", "Use procedural-focused techniques to build and master this type of skill."], "keywords": ["procedural knowledge", "skills", "how-to", "application", "practice", "dynamic"], "additional_context": "Examples include solving a math problem (the theory is declarative, the calculation is procedural) or speaking a language (vocabulary is declarative, fluency is procedural). Most subjects require a mix of both declarative and procedural knowledge."}, {"concept_id": "retrieval_001", "concept_name": "3Cs (<PERSON>, <PERSON><PERSON>, Check)", "summary": "A low-level, 'fill-in-the-blanks' style retrieval technique for memorizing basic information. It is most effective for labeling diagrams and processes.", "ics_phase": "Retrieval", "instructions": ["Cover a part of your notes or a diagram.", "Attempt to rewrite or redraw the covered part from memory.", "Uncover the notes to check your work for accuracy."], "keywords": ["3cs", "cover copy check", "retrieval", "memorization", "lower-order"], "additional_context": "This technique is effective for short-term retention of basic information, especially for younger children. For most learners, flashcards using image-occlusion (like in Anki) are a more practical and efficient variation."}, {"concept_id": "retrieval_002", "concept_name": "Flashcards (Simple, Relational, Evaluative)", "summary": "A versatile retrieval technique using physical or digital cards (like Anki) to test knowledge. Flashcards can be adapted for different levels of learning, from isolated facts to complex evaluations.", "ics_phase": "Retrieval", "instructions": ["For Lower-Order learning (facts, definitions): Create 'Simple' flashcards where each card tests one isolated fact or concept.", "For Mid-Order learning (relationships): Create 'Simple Relational' flashcards that test the relationship between two or three concepts.", "For Higher-Order learning (evaluation): Create 'Evaluative' flashcards that pose a complex question requiring you to discuss, compare, and make a value judgment on the importance of multiple related ideas."], "keywords": ["flashcards", "anki", "spaced repetition", "retrieval practice", "simple", "relational", "evaluative"], "additional_context": "Simple flashcards are best for specific information that doesn't fit into relational notes. Overloading simple flashcards when higher-order encoding is needed leads to wasted time. Digital flashcards like Anki are preferred for their spaced repetition algorithm."}, {"concept_id": "retrieval_003", "concept_name": "Generated Questions (Isolated, Relational, Evaluative)", "summary": "The practice of creating your own questions to test your knowledge at different levels of complexity. This process has significant learning benefits for both question creation and answering.", "ics_phase": "Retrieval", "instructions": ["Create 'Isolated' questions (often 'what' questions) to retrieve single facts or concepts. Use these for simple flashcards.", "Create 'Simple/Multi-relational' questions that challenge you to explain the relationship between two or more ideas.", "Create 'Evaluative' questions that require you to discuss relationships between multiple ideas and conclude on their relative strength and importance."], "keywords": ["generated questions", "self-testing", "question creation", "retrieval", "isolated", "relational", "evaluative"], "additional_context": "Creating questions and answering them are both acts of retrieval. For time-consuming evaluative questions, it is recommended to create them in one session and answer them in another to get two rounds of learning benefits."}, {"concept_id": "retrieval_004", "concept_name": "<PERSON> Dumps (Linear, Mindmap)", "summary": "A mass retrieval technique where you write down everything you know about a topic from memory. It is useful for identifying knowledge gaps, especially at the beginning of a revision cycle.", "ics_phase": "Retrieval", "instructions": ["Choose a topic or concept.", "Set a timer if desired, and write down everything you can recall about it without consulting your notes.", "Use a 'Linear' (text document) format for testing detailed recall.", "Use a 'Mindmap' format to better test higher-order relationships and find big-picture gaps.", "After the dump, compare it to your source material to identify and fill gaps."], "keywords": ["brain dump", "retrieval", "mass retrieval", "mindmap", "knowledge gaps"], "additional_context": "Mindmap brain dumps are generally more high-yield as they force you to think in relationships and chunks. Linear brain dumps can be useful as a final check before an assessment."}, {"concept_id": "retrieval_005", "concept_name": "Teaching as Retrieval (Isolated, Relational, WPW)", "summary": "Using the act of teaching a topic to someone else (real or imaginary) as a method of active retrieval. Explaining concepts forces you to structure your knowledge and identify gaps.", "ics_phase": "Retrieval", "instructions": ["For Lower-Order retrieval: Use 'Isolated Teaching' to explain individual facts, concepts, and processes.", "For Mid-Order retrieval: Use 'Simple Relational Teaching' to explain and discuss how different concepts relate to each other.", "For Advanced retrieval: Use 'Modified Whole-Part-Whole (WPW)' to challenge all orders of knowledge mastery with rapid gap identification (expert level).", "When possible, teach out loud to force yourself to be explicit and slow down, which helps prevent glossing over gaps (illusion of explanatory depth)."], "keywords": ["teaching", "feynman method", "retrieval", "explaining", "imaginary student", "WPW"], "additional_context": "Teaching to an imaginary student is often preferred, as a real person might fill in gaps for you. A 'micro-retrieval' variation involves teaching a concept immediately after learning it to improve deep processing."}, {"concept_id": "retrieval_006", "concept_name": "Active Practice Questions (Direct, Extended)", "summary": "An active and effective way of using practice questions that goes beyond passively checking the answer. It focuses on genuinely testing knowledge and deeply analyzing mistakes.", "ics_phase": "Retrieval", "instructions": ["For the 'Direct Method': Solve questions from memory, check the answer immediately, and review the material if incorrect.", "For the more effective 'Extended Method':", "1. Answer all questions to the best of your ability.", "2. Mark any questions you felt unsure about, regardless of the final answer.", "3. Review your learning material and create your own 'perfect' answer sheet.", "4. Check your answers against the official answers to find even more gaps.", "Always generate full written or typed answers, never just mental ones, to avoid the illusion of explanatory depth."], "keywords": ["practice questions", "active practice", "direct method", "extended method", "testing", "knowledge gaps"], "additional_context": "The direct method risks misclassifying errors as 'silly mistakes' without finding the root cause. The extended method is more time-consuming but far more effective at identifying gaps, as a lack of confidence is a sufficient indicator of a gap."}, {"concept_id": "retrieval_007", "concept_name": "Evaluative Peer/Group Discussion", "summary": "A structured and effective form of group study with the explicit goal of critiquing, judging, and arguing the importance of different concepts and their relationships.", "ics_phase": "Retrieval", "instructions": ["Form a study group with a clear topic.", "Set an explicit goal for the session, such as to critique a particular theory or argue the importance of different concepts.", "Focus the discussion on evaluation and judgment, especially in view of the bigger picture."], "keywords": ["group discussion", "evaluative", "group study", "peer discussion", "critique", "higher-order"], "additional_context": "Unlike undirected discussion, this method is highly effective because it forces higher-order thinking and a deeper engagement with the material. It is a key component of the 'Advanced Group Method'."}, {"concept_id": "encoding_003", "concept_name": "Chunkmaps (GRINDEmaps)", "summary": "Chunkmaps are a specific type of mindmap used as a core part of the encoding process in the iCanStudy system. They are designed to structure information and build higher levels of mastery and retention from the outset.", "ics_phase": "Encoding", "instructions": ["Use the Chunkmap/GRINDEmap technique as your primary method for encoding new information.", "For previously studied content that was poorly encoded, re-encode it into a chunkmap as the first step of revision."], "keywords": ["chunkmaps", "grindemaps", "encoding", "mindmap", "bear hunter system", "note-taking"], "additional_context": "This is an advanced technique taught later in the iCanStudy program (Ascent 3). While it is an encoding method, it can be used as a retrieval method to revise and re-structure poorly learned topics."}, {"concept_id": "retrieval_008", "concept_name": "Rote Memorization Techniques (Loci, Link, Ben System)", "summary": "A set of powerful but narrow techniques for rote learning of isolated, often ordered, information. They are exclusively lower-order methods and should be used sparingly.", "ics_phase": "Retrieval", "instructions": ["Identify a specific use case requiring memorization of a large volume of discrete items (e.g., a long list).", "Apply a suitable technique like the Method of Loci (memory palace), Story/Link method, or Ben system.", "Use these techniques only when other encoding methods are inefficient for the task."], "keywords": ["memorization", "rote learning", "method of loci", "memory palace", "link method", "ben system", "memory champion"], "additional_context": "These techniques are often over-sold. Over-reliance on them can prevent the development of proper, more versatile encoding techniques. They are recommended only for specific, narrow use cases."}, {"concept_id": "retrieval_009", "concept_name": "<PERSON><PERSON><PERSON> Method", "summary": "A guided, four-step learning structure that forces multi-order knowledge mastery by simplifying and explaining a topic as if teaching it to a child. It is excellent for identifying gaps in understanding.", "ics_phase": "Retrieval", "instructions": ["1. <PERSON><PERSON> a Topic: Select the concept you want to understand.", "2. Teach it to a Child: Explain the topic in simple language, avoiding jargon.", "3. Identify Gaps and Simplify: Pay attention to where your explanation is unclear or complex. This reveals your knowledge gaps.", "4. Review and Refine: Go back to your source material to fill the gaps and simplify your explanation further."], "keywords": ["feynman method", "feynman technique", "teaching", "simplifying", "gaps", "understanding"], "additional_context": "While iCanStudy methods may achieve similar effects more consistently, the <PERSON><PERSON><PERSON> method is a great addition to any learning system. It can be used as a 'micro-retrieval' technique by trying to 'teach to a child' immediately after learning something."}, {"concept_id": "retrieval_010", "concept_name": "Advanced Group Method for Practice Questions", "summary": "A highly effective, composite retrieval technique for declarative subjects that combines generated questions, peer discussion, and practice testing. It tests multiple orders of knowledge from multiple perspectives.", "ics_phase": "Retrieval", "instructions": ["Choose a topic to study.", "Form a study group with at least one person of a similar or higher knowledge level.", "Each person individually creates a practice exam with mid- to higher-order questions.", "Each person creates a 'perfect' answer sheet for their own exam by brain dumping and then refining with learning materials.", "Swap practice exams, but keep your own answer sheet.", "Answer the exam you received from memory to the best of your ability.", "Mark any answers where you felt uncertain.", "Create a 'perfect' answer sheet for the exam you just took by reviewing learning materials.", "Swap the answer sheets back so everyone has their original exam and the two answer sheets.", "Compare your answers against your own 'perfect' sheet and your partner's 'perfect' sheet to find differences.", "Discuss any points of disagreement with your partner to arrive at a conclusion."], "keywords": ["advanced group method", "group study", "practice questions", "generated questions", "peer discussion", "declarative", "exam", "testing"], "additional_context": "This method is time-consuming but has one of the highest yields for learning consolidation and gap-testing. It is recommended to split the process over multiple days. It forces positive generative effects at multiple steps of the process."}, {"concept_id": "retrieval_011", "concept_name": "Retrieved Execution (Simple, Integrative, Applied)", "summary": "A set of procedural learning techniques based on practicing a skill from memory. The complexity scales from executing an isolated skill to applying multiple skills in a real-world project.", "ics_phase": "Retrieval", "instructions": ["Start with 'Simple Retrieved Execution': Practice a single, isolated skill or process from memory without variation (e.g., writing a basic 'for' loop).", "Progress to 'Integrative Retrieved Execution': Combine multiple skills and processes to perform a more complex function (e.g., writing a function that uses several loops and conditionals).", "Advance to 'Applied Retrieved Execution': Start with a real-world goal or project and work backwards, strategizing and combining multiple integrated components to achieve the target output (e.g., building a complete web application)."], "keywords": ["retrieved execution", "procedural learning", "skill practice", "simple", "integrative", "applied", "coding", "language"], "additional_context": "Learners should move from simple to integrative execution as soon as they have basic competency. Applied retrieved execution is the most challenging but most effective form of practice."}, {"concept_id": "retrieval_012", "concept_name": "Challenges (Simple, Integrative, Edge-case)", "summary": "A procedural practice method that involves solving a prompt or problem using your skills. Challenges are more guided than retrieved execution and can be designed to test specific aspects of your knowledge.", "ics_phase": "Retrieval", "instructions": ["Use 'Simple Challenges' to build basic procedural competency with simple applications.", "Use 'Integrative Challenges' to practice combining multiple skills to solve a more complex problem (e.g., multi-step physics problems).", "Use 'Edge-case Challenges' to test your understanding of principles in atypical, unfamiliar contexts. These require strong declarative knowledge to navigate."], "keywords": ["challenges", "problem solving", "procedural", "integrative", "edge-case", "hackathon", "competition"], "additional_context": "Creating edge-case challenges is often easier than solving them. This means you can use them in a group setting (like the Advanced Group Method) by having participants create and swap edge-case problems."}, {"concept_id": "retrieval_013", "concept_name": "Variable Modification and Addition", "summary": "A technique for extending the life of practice problems by changing or adding variables. This allows for repeated practice with increasing difficulty without needing new source material.", "ics_phase": "Retrieval", "instructions": ["Use 'Variable Modification': Take an existing problem and change the values or simple variables, then solve it again. The approach remains the same.", "Use 'Variable Addition': Take an existing problem and add a new variable, constraint, or context to increase its difficulty and make it a new, more complex problem to solve (e.g., adding air resistance to a physics problem)."], "keywords": ["variable modification", "variable addition", "practice problems", "procedural", "difficulty", "interleaving"], "additional_context": "Variable addition is a powerful way to generate learning effects, as it requires you to think about how variables interact. AI can be a useful tool for creating more complex variations of existing problems."}, {"concept_id": "enabler_001", "concept_name": "Procedural-focused Learning Strategy", "summary": "A straightforward learning strategy for subjects that are mostly procedural. The approach involves progressing from lower-order to higher-order practice methods as skills are built.", "ics_phase": "Enablers", "instructions": ["Start with lower-order methods like 'Simple Retrieved Execution' to build foundational competency.", "Progressively move towards higher-order methods like 'Integrative Execution', 'Challenges', and 'Applied Execution'.", "Continuously increase the difficulty and variation of your practice."], "keywords": ["procedural learning", "learning plan", "strategy", "skill acquisition"], "additional_context": "This strategy is simple because procedural skills build on themselves, and lower-order competence is directly tested by higher-order methods. The path to mastery is straightforward: consistent practice with increasing difficulty and feedback."}, {"concept_id": "enabler_002", "concept_name": "Declarative-focused Learning Strategy (Interleaving Plan)", "summary": "A structured, comprehensive schedule for learning declarative knowledge, progressing from initial encoding to multi-order retrieval over several weeks. It provides a framework for when to use different retrieval techniques.", "ics_phase": "Enablers", "instructions": ["Before the topic: Prestudy for a big-picture frame.", "Day 1 (Learning Event): Use proper encoding techniques (e.g., Chunkmaps).", "Day 1 (Review): Use 'micro-retrieval' (e.g., Simple Relational Teaching) as you learn.", "Days 2-5 (First Retrieval): Do a Mindmap Brain Dump to find big gaps. Create flashcards for small gaps.", "Ongoing: Review flashcards in small pockets of time.", "Days 7-14 (Second Retrieval): Create relational/evaluative questions and perfect answer sheets.", "One Month (Third Retrieval): Use the Advanced Group Method with a partner.", "Two Months (Fourth Retrieval): Engage in Evaluative Discussion or WPW teaching."], "keywords": ["learning plan", "schedule", "declarative knowledge", "interleaving", "study system", "retrieval schedule"], "additional_context": "This is a guide, not a rigid rule; adapt it based on your needs. The goal is to build a foundation and progressively consolidate knowledge using more complex retrieval methods over time."}, {"concept_id": "enabler_003", "concept_name": "Mixed Knowledge Learning Strategy", "summary": "A strategy for subjects with both declarative and procedural requirements. It begins with a focus on mastering the declarative components before transitioning to exclusively procedural practice.", "ics_phase": "Enablers", "instructions": ["Start by using the declarative-focused strategy to build a strong foundation of the concepts and principles.", "Once you have good declarative competence, transition fully to the procedural-focused strategy.", "Progress from lower-order to higher-order procedural methods (e.g., Simple Execution -> Applied Execution)."], "keywords": ["mixed knowledge", "learning strategy", "declarative", "procedural", "transition"], "additional_context": "Declarative knowledge is often the most time-consuming part to master. For procedurally heavy topics, the declarative phase should be much faster. In some cases, strong initial encoding is enough for the declarative part."}, {"concept_id": "enabler_004", "concept_name": "The Principle of Active Production in Language Learning", "summary": "A core principle stating that learners should not withhold active practice (speaking and writing) in a foreign language. Immediate and consistent retrieved execution is crucial for effective language acquisition and goes against popular but misguided theories.", "ics_phase": "Enablers", "instructions": ["Do not wait until you 'feel ready' or have 'mastered' the language to start speaking or writing.", "Engage in active production (retrieved execution) as early and as often as possible.", "Use procedural learning techniques (Simple, Integrative, and Applied Execution) for language practice."], "keywords": ["language learning", "active practice", "speaking", "writing", "comprehensible input", "immersion"], "additional_context": "The idea of a 'silent period' is a misinterpretation of learning research. There is no known benefit from withholding retrieved execution practice for language learning. All evidence points to the necessity of active production for fluency."}, {"concept_id": "enabler_005", "concept_name": "The Principle of Full Answer Generation", "summary": "A fundamental rule for all retrieval practice: always generate complete answers, either written or typed. Answering mentally is insufficient and leads to an overestimation of one's knowledge.", "ics_phase": "Enablers", "instructions": ["When answering a practice question, using a flashcard, or doing a brain dump, always generate a full answer.", "This can be typed or handwritten. The format does not matter as much as the cognitive process of full generation.", "Never answer 'in your head' or stop once you think you know the answer."], "keywords": ["full answer", "generation", "retrieval practice", "illusion of explanatory depth", "writing", "typing"], "additional_context": "This principle prevents knowledge gaps caused by the 'illusion of explanatory depth,' a cognitive bias where we think we understand something in greater detail than we actually do. Forcing full generation makes gaps obvious."}]