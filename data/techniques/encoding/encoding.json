[{"concept_id": "encoding_001", "concept_name": "Feynman Technique", "summary": "A learning method that involves explaining concepts in simple terms as if teaching someone else, revealing gaps in understanding.", "ics_phase": "Encoding", "instructions": ["Choose a concept you want to understand better.", "Write an explanation of the concept as if you're teaching it to a child or someone with no background knowledge.", "Use simple language and avoid jargon.", "When you get stuck or use complex terms, go back to your source material.", "Refine your explanation until you can explain it simply and completely."], "keywords": ["explanation", "teaching", "simplification", "understanding", "gaps", "clarity"], "additional_context": "The Feynman Technique works because teaching forces you to organize knowledge clearly and identify gaps. If you can't explain it simply, you don't understand it well enough."}, {"concept_id": "encoding_002", "concept_name": "Concept Mapping", "summary": "Creating visual diagrams that show relationships between concepts, helping to organize and connect knowledge.", "ics_phase": "Encoding", "instructions": ["Start with a central concept in the middle of a page.", "Add related concepts around it, connecting them with labeled lines.", "Use phrases on the connecting lines to describe relationships (e.g., 'causes', 'is part of', 'leads to').", "Continue adding concepts and connections until you have a comprehensive map.", "Review and refine the map, looking for missing connections or concepts."], "keywords": ["visual", "relationships", "connections", "organization", "structure", "mapping"], "additional_context": "Concept maps help you see the big picture and understand how individual pieces fit together. They're especially useful for complex topics with many interconnected parts."}, {"concept_id": "encoding_003", "concept_name": "Analogical Reasoning", "summary": "Using familiar concepts or experiences to understand new, complex ideas by drawing parallels and comparisons.", "ics_phase": "Encoding", "instructions": ["When encountering a new concept, ask 'What is this similar to that I already know?'", "Identify the key features of both the new concept and the familiar one.", "Map the similarities and differences between them.", "Use the analogy to predict how the new concept might behave or function.", "Test your understanding by extending the analogy to new situations."], "keywords": ["analogy", "comparison", "similarity", "familiar", "mapping", "understanding"], "additional_context": "Analogies help bridge the gap between known and unknown by leveraging existing knowledge structures. Good analogies can make complex concepts intuitive and memorable."}, {"concept_id": "encoding_004", "concept_name": "Dual Coding", "summary": "Combining verbal and visual information processing to enhance understanding and memory through multiple representation channels.", "ics_phase": "Encoding", "instructions": ["For any concept you're learning, create both verbal and visual representations.", "Write out key points in words, then create diagrams, charts, or drawings.", "Use colors, symbols, and spatial arrangements in your visual representations.", "Practice switching between verbal explanations and visual descriptions.", "Test yourself using both verbal questions and visual interpretation tasks."], "keywords": ["visual", "verbal", "representation", "multiple", "channels", "memory"], "additional_context": "Dual coding theory suggests that information processed through both verbal and visual channels is better remembered and understood than information processed through only one channel."}, {"concept_id": "encoding_005", "concept_name": "Elaborative Processing", "summary": "Connecting new information to existing knowledge and personal experiences to create richer, more memorable representations.", "ics_phase": "Encoding", "instructions": ["For each new piece of information, ask 'How does this relate to what I already know?'", "Think of personal experiences that connect to the concept.", "Generate examples from your own life or interests.", "Create stories or scenarios that incorporate the new information.", "Discuss the concept with others, sharing your personal connections."], "keywords": ["connections", "personal", "experiences", "elaboration", "meaning", "relevance"], "additional_context": "Elaborative processing creates multiple retrieval pathways by connecting new information to existing knowledge networks. Personal relevance makes information more meaningful and memorable."}]