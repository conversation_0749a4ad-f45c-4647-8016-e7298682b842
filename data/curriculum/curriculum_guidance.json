[{"concept_id": "curriculum_enablers", "concept_type": "CurriculumGuidance", "concept_name": "Foundational & Self-Management Techniques (Enablers)", "summary": "Core skills focused on creating the time, focus, and mental readiness for effective learning. Covered in Rapid Start and Briefing modules.", "key_techniques": ["Task Prioritization (Urgent vs. Important)", "Strategic Scheduling", "Focus & Environment Management (BEDS-M Framework)", "Minimum Viable Goals (MVG)", "Make a List (Break It Down)", "Set Yourself Up / Get Started Beforehand", "Growth Habit Mapping"], "coaching_priority": "CRITICAL - Must be mastered before advancing to Retrieval phase", "success_indicators": ["User has consistent daily schedule", "Procrastination episodes reduced significantly", "Environment optimized for focus", "Can break down complex tasks into manageable steps"], "academic_applications": {"computational_linguistics": ["Schedule regular programming practice sessions", "Break down complex algorithm implementations", "Manage multiple language learning projects"], "phonetics_linguistics": ["Organize phonetic transcription practice", "Schedule regular listening exercises", "Plan fieldwork and data collection sessions"]}}, {"concept_id": "curriculum_retrieval", "concept_type": "CurriculumGuidance", "concept_name": "Core Memory & Retrieval Techniques (The SIR Loop)", "summary": "Techniques that form the 'safety net' of learning system, designed to strengthen memory and identify knowledge gaps. Central to Fundamentals 1 & 2 modules.", "key_techniques": ["Spaced Repetition", "Interleaving", "Active Retrieval (Teaching, Brain Dumps, Practice Questions)", "3Cs (<PERSON>, <PERSON><PERSON>, Check)", "Flashcards (Simple, Relational, Evaluative)"], "coaching_priority": "HIGH - Creates foundation for deep learning", "success_indicators": ["Consistent spaced repetition schedule", "Can teach concepts in own words", "Creates challenging practice questions", "Memory retention significantly improved"]}, {"concept_id": "curriculum_encoding", "concept_type": "CurriculumGuidance", "concept_name": "Deep Understanding & Advanced Techniques (Encoding)", "summary": "Techniques for processing information at deeper, conceptual level to build expert-level knowledge. Taught in Briefing and Technique Training stages.", "key_techniques": ["Collecting vs. Processing (Advanced Note-Taking)", "Non-linear Note-Taking (Mindmaps, Diagrams)", "Chunkmaps (GRINDEmaps)", "Pre-study", "Traffic Light System (TLS)", "Order Control", "Feynman Technique", "Variable Modification & Addition"], "coaching_priority": "ADVANCED - Only after Enablers and Retrieval are stabilized", "success_indicators": ["Can explain complex concepts simply", "Creates interconnected knowledge structures", "Uses inquiry-based learning naturally", "Demonstrates expert-level understanding"]}, {"concept_id": "progression_logic", "concept_type": "CurriculumGuidance", "concept_name": "3-Phase Concurrent Progression Model", "summary": "The coach operates on concurrent phase logic - user works on all three phases but with emphasis shifting based on main focus and skill development.", "progression_rules": ["Always assess Enabler skills before suggesting advanced techniques", "If user struggles with higher-order skill, first check if foundational system is failing", "Never recommend advanced Encoding if Retrieval 'safety net' is not in place", "Frame journey in terms of three pillars so user understands the 'why'"], "main_focus_guidance": ["Enablers Focus: Emphasize time management, focus, procrastination solutions", "Retrieval Focus: Emphasize memory techniques, active recall, spaced repetition", "Encoding Focus: Emphasize deep understanding, concept mapping, inquiry-based learning"], "advancement_criteria": ["Enablers → Retrieval: Consistent schedule, reduced procrastination, optimized environment", "Retrieval → Encoding: Strong memory techniques, can teach concepts, creates good questions", "Within phases: Technique mastery, confidence scores stable, ready for more challenge"]}, {"concept_id": "technique_selection", "concept_type": "CurriculumGuidance", "concept_name": "Intelligent Technique Recommendation System", "summary": "Guidelines for selecting appropriate techniques based on user's current state, challenges, and learning context.", "selection_criteria": ["Current phase focus and skill levels", "Specific challenges user is facing", "Academic context (subjects, deadlines, difficulty)", "Learning preferences and past success patterns", "Available time and energy levels"], "recommendation_strategies": ["For procrastination: MVG, timeboxing, environment design", "For memory issues: Spaced repetition, active recall, teaching", "For understanding problems: <PERSON><PERSON><PERSON> technique, concept mapping, TLS", "For overwhelm: Task breakdown, prioritization, scheduling", "For plateaus: REBIM detection, advancement to higher-order techniques"]}, {"concept_id": "mastery_assessment", "concept_type": "CurriculumGuidance", "concept_name": "Technique Mastery and Advancement Criteria", "summary": "Clear criteria for determining when a user has mastered a technique and is ready to advance to more challenging methods.", "mastery_indicators": ["Confidence scores consistently high (4-5/5)", "Can apply technique without conscious effort", "Can teach the technique to others", "Technique feels 'easy' or 'automatic'", "User reports boredom or lack of challenge"], "advancement_triggers": ["REBIM detection: Same technique, plateaued progress", "Mastery achieved: Ready for higher-order version", "Foundation solid: Can add complexity or new techniques", "User request: Explicitly asks for more challenge"], "coaching_approach": ["Celebrate mastery achievement", "Explain why advancement is beneficial", "Connect new technique to existing knowledge", "Provide clear practice plan for new technique"]}]