[{"concept_id": "module_01", "concept_name": "Module 1: Rapid Start", "summary": "This is the foundational stage focused on building and stabilizing the 'Enabler' skills. It addresses the most common barriers to effective learning: poor time management, lack of focus, and procrastination. Mastering this stage is non-negotiable for future progress.", "ics_phase": "Enablers", "instructions": ["1. Differentiate between 'Urgent' and 'Important' tasks to escape the 'Urgency Trap'.", "2. Create a realistic and sustainable weekly schedule using a calendar, ensuring not to overschedule.", "3. Use the BEDS-M framework (or at least Environment and Scheduling) to create a focused study environment.", "4. Practice anti-procrastination techniques like 'Make a List' and 'Minimum Viable Goals (MVG)'.", "5. Use 'Growth Habit Mapping' to become aware of your mindset and reactions to challenges."], "keywords": ["time management", "focus", "procrastination", "scheduling", "enablers", "urgency trap", "mindset", "foundations"], "additional_context": "The goal of this module is to create the time and mental space necessary for learning. The coach's primary objective here is to help the user build consistency. The user should not proceed until they have a stable schedule and have begun to manage their main distractions."}, {"concept_id": "module_02", "concept_name": "Module 2: Fundamentals 1 (Intro to SIR)", "summary": "This module introduces the core retrieval practice loop: Spaced repetition, Interleaving, and Retrieval (SIR). It establishes the 'safety net' that prevents forgetting and builds a consistent habit of active learning.", "ics_phase": "Retrieval", "instructions": ["1. Implement a simple Spaced Repetition schedule: review on the same day, end of the week, and end of the month.", "2. During revision, use basic Interleaving techniques: switch between teaching, drawing a mindmap, and answering practice questions.", "3. Apply the principles of active Retrieval: always attempt to recall from memory first before checking notes.", "4. Practice 'micro-retrieval' during initial learning by thinking and processing before writing anything down."], "keywords": ["sir", "spaced repetition", "interleaving", "retrieval", "active recall", "revision", "memory", "forgetting curve"], "additional_context": "This is the first layer of active learning. The techniques are meant to be simple and easy to implement. The focus is on doing them consistently within the schedule created in the 'Rapid Start' module. It's better to do simple techniques regularly than complex ones sporadically."}, {"concept_id": "module_03", "concept_name": "Module 3: Fundamentals 2 (Systematizing Practice)", "summary": "This module refines the SIR system by applying it to real-world academic contexts like preparing for class and exams. It introduces early encoding techniques to make learning more efficient and less reliant on rote memorization.", "ics_phase": "Retrieval & Encoding", "instructions": ["1. Implement Pre-study before every lecture or new topic, focusing on concepts and relationships, not details.", "2. Improve in-class note-taking by paraphrasing and using non-linear formats (mindmaps, diagrams) instead of writing verbatim.", "3. Structure revision sessions using the 'Test, Target, Teach' framework.", "4. Learn to think like an examiner by creating your own challenging questions to expose weaknesses."], "keywords": ["pre-study", "note-taking", "exam technique", "revision strategy", "paraphrasing", "mindmap", "teaching"], "additional_context": "This stage bridges the gap between passive learning and truly efficient studying. The goal is to reduce the total time needed for revision by improving the quality of the initial learning (encoding) that happens in and before class."}, {"concept_id": "module_04", "concept_name": "Module 4: Briefing (Developing Metacognition)", "summary": "This module is a critical transition that focuses on developing metacognitive awareness—the ability to think about your own thinking. It explains the 'why' behind effective techniques, shifting the user from just following instructions to understanding the principles.", "ics_phase": "Encoding", "instructions": ["1. Actively use the 'Collecting vs. Processing' note-taking method to separate information gathering from sense-making.", "2. Practice 'Order Control' by allowing your curiosity and questions to dictate the order in which you learn material, rather than following a textbook linearly.", "3. Use questions like 'Why is this important?' and 'How does this relate to X?' to activate the 'Curiosity Switch'.", "4. Apply <PERSON><PERSON><PERSON>'s Experiential Learning Cycle to reflect on practice, identify errors, and plan better experiments."], "keywords": ["metacognition", "curiosity", "order control", "processing", "reflection", "kol<PERSON>'s cycle", "deeper understanding"], "additional_context": "This is where the user learns to become their own coach. The techniques here are less about 'what to do' and more about 'how to think'. Mastery of this stage is essential before moving to the most advanced encoding methods."}, {"concept_id": "module_05", "concept_name": "Module 5: Technique Training (Advanced Encoding)", "summary": "This module introduces the most powerful encoding techniques for building deep, interconnected, and expert-level knowledge structures. It leverages core principles of cognitive psychology to make learning organic and highly efficient.", "ics_phase": "Encoding", "instructions": ["1. Use the 'Traffic Light System' (TLS) to structure your study sessions around inquiry-based learning (Ask questions -> Find answers -> Ask better questions).", "2. Apply the 'Survive and Thrive' filter to all new information by constantly asking how it can be used to solve a problem.", "3. Move from simple mindmaps to advanced 'Chunkmaps' (or GRINDEmaps) that focus on the relationships between concepts.", "4. Understand the 'Cave Theory' to recognize why problem-based, interconnected learning is more natural and effective than artificial, linear learning."], "keywords": ["traffic light system", "tls", "inquiry-based learning", "chunking", "chunkmaps", "problem-solving", "conceptual learning", "expert"], "additional_context": "This is the pinnacle of the initial learning journey. By this stage, the user should be moving away from a reliance on rote retrieval and towards building knowledge that is so well-structured it is difficult to forget. The 'Interleaving Table Full' document serves as a master reference for applying these principles to different subjects."}]