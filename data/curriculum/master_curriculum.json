{"curriculum_name": "iCanStudy Complete Learning System", "curriculum_version": "1.0", "description": "Complete curriculum from Module 0 through Module 4 with clear progression path", "terminology_notes": {"spacing": "We use 'spacing' not 'spaced repetition' - spacing refers to timing intervals for retrieval practice", "retrieval": "Active recall from memory, not passive review", "sir": "Spacing, Interleaving, and Retrieval - the core method"}, "modules": [{"module_id": "module_00", "module_name": "Module 0: Rapid Start", "module_objective": "Clear the most common obstacles that hinder learning progress. Establish strong foundation in self-management, strategic prioritization, and focus control.", "prerequisites": "None - starting point for all users", "estimated_duration": "2-4 weeks", "completion_criteria": "Stabilizing skill level in all enabler competencies", "units": [{"unit_id": "unit_1", "unit_name": "Core Philosophy & Foundational Mindsets", "unit_goal": "Understand the 'why' behind this module and adopt core mindsets for effective learning", "topics": [{"topic_id": "1.1", "topic_name": "The Purpose of Rapid Start: Unblocking Your Path", "concept": "Investing time now to remove barriers for faster, less frustrating progress later", "key_principle": "Slightly slower start for rapid progress"}, {"topic_id": "1.2", "topic_name": "The Real Engine of Learning: The Theory-Practice Cycle", "concept": "Shift from 'learn theory first' to effective cycle: Basic Theory → Practice to Find Errors → Deeper Understanding", "key_principle": "Practice reveals gaps that theory alone cannot"}, {"topic_id": "1.3", "topic_name": "Mindset for Action: Personal Best, Not Perfection", "concept": "Combat perfectionism-induced procrastination by focusing on process-oriented goals", "key_principle": "10 minutes of full focus beats perfect outcomes"}]}, {"unit_id": "unit_2", "unit_name": "Strategic Time & Task Management", "unit_goal": "Master effective prioritization and scheduling to escape 'busy but not productive' cycle", "topics": [{"topic_id": "2.1", "topic_name": "The Foundational Principle: <PERSON><PERSON> vs. <PERSON>", "concept": "Actively distinguish between tasks demanding immediate attention vs. tasks contributing to long-term goals", "key_principle": "Important tasks build the future, urgent tasks manage the present"}, {"topic_id": "2.2", "topic_name": "Escaping the Urgency Trap", "concept": "Identify and schedule important, non-urgent tasks first, fit urgent tasks around them", "key_principle": "Schedule importance first, urgency second"}, {"topic_id": "2.3", "topic_name": "Practical Scheduling Techniques", "concept": "Build realistic, manageable calendar including time for relaxation and fun", "key_principle": "Overscheduling leads to failure, realistic scheduling leads to success", "action_item": "Complete Action Plan - Rapid Time Management"}]}, {"unit_id": "unit_3", "unit_name": "Overcoming Procrastination & Mastering Focus", "unit_goal": "Implement practical techniques to manage focus, eliminate distractions, take consistent action", "topics": [{"topic_id": "3.1", "topic_name": "Quick-Start Anti-Procrastination Techniques", "concepts": ["Make a List (Break It Down)", "Set Yourself Up for Success (Environment Prep)", "Get Started Beforehand (Mini-Advance)", "Group Up (Social Focus)"]}, {"topic_id": "3.2", "topic_name": "The BEDS-M Framework for Deep Focus", "concepts": {"burnt_ships": "High-stakes method for urgent behavior change", "environment": "Most powerful strategy - optimize physical and digital space", "distraction_sheet": "Simple tool to track interruptions and inform changes", "scheduling": "Use calendar as psychological tool for focus", "minimum_viable_goals": "Ultimate technique to break inertia with ridiculously small steps"}}, {"topic_id": "3.3", "topic_name": "Task Execution & Reflection", "action_item": "Implement Action Plan: Goodbye Procrastination! on a real task"}]}, {"unit_id": "unit_4", "unit_name": "Building the Engine for Skill Development", "unit_goal": "Establish core, repeatable habits that will drive all future skill acquisition", "topics": [{"topic_id": "4.1", "topic_name": "Self-Awareness for Growth: Habit Mapping", "concept": "10-minute reflective exercise to identify default responses to challenge and failure", "key_principle": "Awareness of patterns enables change"}, {"topic_id": "4.2", "topic_name": "The Core Method: Scheduling Practice Blocks", "concept": "Central technique - dedicated, consistent time blocks focused on finding and fixing mistakes", "key_principle": "Consistent practice blocks are the engine of skill development"}, {"topic_id": "4.3", "topic_name": "Scheduling for Mastery: The 5:1 Ratio", "concept": "Schedule significantly more time for active practice than theory consumption", "key_principle": "5:1 ratio of practice to theory for true competence"}]}, {"unit_id": "unit_5", "unit_name": "Module Checkpoint & Consolidation", "unit_goal": "Assess mastery of enabler skills and ensure readiness for next phase", "topics": [{"topic_id": "5.1", "topic_name": "Recommended Tools & Resources", "concept": "Curated list of applications for task management, focus, note-taking"}, {"topic_id": "5.2", "topic_name": "The Rapid Start Checkpoint: Self-Assessment", "assessment_areas": ["Evaluating tasks based on importance vs. urgency", "Scheduling to prevent urgency trapping", "Using calendar to create manageable schedules", "Managing focus and procrastination"], "goal": "Reach 'Stabilising Skill' level for core competencies before advancing"}]}]}, {"module_id": "module_01", "module_name": "Module 1: Fundamentals 1 - SIR (Spacing, Interleaving, Retrieval)", "module_objective": "Master the core engine of effective learning: Spacing, Interleaving, and Retrieval (SIR)", "prerequisites": "Module 0 completed - enabler skills stabilized", "estimated_duration": "3-4 weeks", "completion_criteria": "Able to design and implement personalized SIR schedule", "key_concepts": {"sir_definition": "Spacing (timing intervals), Interleaving (multiple perspectives), Retrieval (active recall)", "terminology": "We use 'spacing' not 'spaced repetition' - focus is on retrieval practice timing"}, "units": [{"unit_id": "unit_1", "unit_name": "Introduction to SIR - The Core Engine", "topics": [{"topic_id": "1.1", "topic_name": "Understanding SIR as a Method", "concept": "SIR is a method, not a single technique - integrates spacing, interleaving, and retrieval", "key_principle": "Two sides of learning coin: Encoding and Retrieval"}, {"topic_id": "1.2", "topic_name": "Deep Dive: Spacing", "concept": "Optimal timing for retrieval practice to combat forgetting curve", "intervals": "Same day, 2-3 days, 1 week, 1 month", "key_principle": "Spacing intervals optimize memory consolidation"}, {"topic_id": "1.3", "topic_name": "Deep Dive: Interleaving", "concept": "Review from multiple angles and perspectives, not simple repetition", "key_principle": "Expertise is structured knowledge network, not isolated facts"}, {"topic_id": "1.4", "topic_name": "Deep Dive: Retrieval", "concept": "Active recall from memory - moving information from long-term to working memory", "types": "Dedicated sessions, micro-retrieval, retrieval during execution", "key_principle": "Retrieval strengthens memory pathways"}]}, {"unit_id": "unit_2", "unit_name": "The Learning Mindset", "topics": [{"topic_id": "2.1", "topic_name": "The Problem with Traditional Studying", "concept": "Study systems built solely on repetition are insufficient", "key_principle": "Recognize <PERSON><PERSON>-<PERSON><PERSON><PERSON> in own learning"}]}, {"unit_id": "unit_3", "unit_name": "Marginal Gains Framework", "topics": [{"topic_id": "3.1", "topic_name": "Introduction to <PERSON><PERSON><PERSON>", "concept": "Small, consistent, well-tracked improvements compound over time", "key_principle": "Consistency superior to sudden bursts of effort"}, {"topic_id": "3.2", "topic_name": "Tracking <PERSON><PERSON><PERSON>", "concept": "5-step framework for identifying and tracking gains", "key_principle": "Tracking small progress essential for motivation"}]}]}, {"module_id": "module_02", "module_name": "Module 2: Fundamentals 2 - Systematizing Practice", "module_objective": "Apply SIR to real-world academic contexts and introduce early encoding techniques", "prerequisites": "Module 1 completed - SIR method understood and practiced", "estimated_duration": "3-4 weeks", "completion_criteria": "Implementing systematic study workflow with encoding techniques", "units": [{"unit_id": "unit_1", "unit_name": "Core Techniques for Active Learning & Encoding", "topics": [{"topic_id": "1.1", "topic_name": "Optimizing In-Class Experience", "concepts": ["Pre-study: Conceptual focus, not detail-oriented", "Paraphrasing as default habit", "Embracing desirable difficulty"]}, {"topic_id": "1.2", "topic_name": "Advanced Note-Taking for Deeper Processing", "concepts": ["Non-verbal & non-linear note-taking", "Mind maps and visual representation", "Starting with baby steps"]}]}, {"unit_id": "unit_2", "unit_name": "Strategic Exam Preparation & Revision", "topics": [{"topic_id": "2.1", "topic_name": "Mindset and Performance Enablers", "concepts": ["Sleep as essential tool for memory consolidation", "Anxiety mitigation with physiological control", "In-exam time management: priority-checking"]}, {"topic_id": "2.2", "topic_name": "Building Effective Study Schedule", "concepts": ["Principles of spacing (1 day, 1 week, 1 month guideline)", "Snowball method of learning", "Calendar role in planning and protecting time"]}, {"topic_id": "2.3", "topic_name": "Advanced Retrieval & Revision Strategies", "concepts": ["Target principle: Start with weaknesses", "Teach principle (Feynman Technique)", "Test principle: Think like examiner", "Combining self-made and pre-made questions"]}]}]}, {"module_id": "module_03", "module_name": "Module 3: Briefing - Developing Metacognition", "module_objective": "Develop metacognitive awareness and understand the 'why' behind effective techniques", "prerequisites": "Module 2 completed - systematic practice established", "estimated_duration": "2-3 weeks", "completion_criteria": "Shift from following instructions to understanding principles", "units": [{"unit_id": "unit_1", "unit_name": "Foundational Mindset - Why Traditional Learning Fails", "topics": [{"topic_id": "1.1", "topic_name": "Flaw of Traditional Note-Taking", "concept": "Continuous, linear note-taking hinders retention and creates false security"}, {"topic_id": "1.2", "topic_name": "Cognitive Load & Mental Processing", "concept": "Importance of thinking about information before writing"}, {"topic_id": "1.3", "topic_name": "Cognitive Decoupling", "concept": "Separate 'writing notes' from 'learning' to supercharge effectiveness"}]}, {"unit_id": "unit_2", "unit_name": "Active Note-Taking & Processing", "topics": [{"topic_id": "2.1", "topic_name": "Processing vs. Collecting", "concept": "Difference between passively collecting vs. actively processing information"}, {"topic_id": "2.2", "topic_name": "Two-Column Technique", "concept": "Collecting on left, Processing on right - forces active engagement"}]}]}, {"module_id": "module_04", "module_name": "Module 4: Technique Training - Advanced Encoding", "module_objective": "Master powerful encoding techniques for building deep, interconnected knowledge", "prerequisites": "Module 3 completed - metacognitive awareness developed", "estimated_duration": "4-5 weeks", "completion_criteria": "Expert-level knowledge structures that are difficult to forget", "units": [{"unit_id": "unit_1", "unit_name": "Core Philosophy - Learning Like a Human", "topics": [{"topic_id": "1.1", "topic_name": "Cave Theory", "concept": "Disconnect between brain's ancient wiring and modern learning environments"}, {"topic_id": "1.2", "topic_name": "Nature of Memory", "concept": "Memory as network of connected nodes - conceptual learning superior to rote"}]}, {"unit_id": "unit_2", "unit_name": "Inquiry-Based Learning", "topics": [{"topic_id": "2.1", "topic_name": "Traffic Light System (TLS)", "concept": "Red Light: Generate questions. Green Light: Hunt for answers. Promotes inquiry-based learning", "key_principle": "Questions drive learning, not passive consumption"}, {"topic_id": "2.2", "topic_name": "Order Control", "concept": "Allow curiosity to dictate learning sequence for maximum efficiency", "key_principle": "Optimal learning path is personal, rarely linear"}]}]}]}