[{"curriculum_id": "ics_main_course", "curriculum_name": "iCanStudy Complete Learning System", "curriculum_type": "course", "summary": "Comprehensive learning methodology covering all three phases of skill development with structured progression.", "ics_phase": "curriculum", "total_estimated_hours": 40, "three_pillar_system": {"max_concurrent_techniques": 3, "pillar_distribution": {"enablers": 1, "retrieval": 1, "encoding": 1}, "progression_rules": ["Always assess Enabler skills before suggesting advanced techniques", "If user struggles with higher-order skill, first check if foundational system is failing", "Never recommend advanced Encoding if Retrieval 'safety net' is not in place", "Frame journey in terms of three pillars so user understands the 'why'"]}, "academic_specializations": {"computational_linguistics": {"keywords": ["programming", "algorithms", "nlp", "machine learning", "python", "linguistics", "syntax", "semantics"], "specific_challenges": ["Complex algorithm implementation", "Multiple programming languages", "Theoretical concepts + practical coding", "Research paper analysis"], "recommended_techniques": {"enablers": ["Strategic Scheduling", "Task Breakdown", "Environment Design"], "retrieval": ["Spaced Repetition for syntax", "Active Recall for algorithms"], "encoding": ["Concept Mapping for architectures", "<PERSON><PERSON><PERSON> for complex theories"]}}, "phonetics_linguistics": {"keywords": ["phonetics", "phonology", "ipa", "transcription", "acoustic", "articulatory", "spanish", "romance languages"], "specific_challenges": ["Auditory discrimination training", "IPA transcription accuracy", "Cross-linguistic comparison", "Fieldwork data analysis"], "recommended_techniques": {"enablers": ["Scheduled listening practice", "Environment optimization for audio"], "retrieval": ["Spaced repetition for IPA", "Active recall for sound patterns"], "encoding": ["Visual mapping of sound systems", "Comparative analysis frameworks"]}}}, "modules": [{"module_id": "module_01_enablers", "module_name": "Phase 1: Enablers - Building Your Foundation", "module_order": 1, "estimated_hours": 15, "description": "Establish robust self-management systems and overcome common learning obstacles", "learning_objectives": ["Master time management and planning techniques", "Develop awareness of growth-limiting habits", "Build consistent practice routines", "Overcome procrastination and resistance"], "coaching_priority": "CRITICAL - Must be mastered before advancing to Retrieval phase", "success_indicators": ["User has consistent daily schedule", "Procrastination episodes reduced significantly", "Environment optimized for focus", "Can break down complex tasks into manageable steps"], "key_techniques": ["Task Prioritization (Urgent vs. Important)", "Strategic Scheduling", "Focus & Environment Management (BEDS-M Framework)", "Minimum Viable Goals (MVG)", "Make a List (Break It Down)", "Set Yourself Up / Get Started Beforehand", "Growth Habit Mapping"], "lessons": [{"lesson_id": "lesson_01_01", "lesson_name": "Growth Habit Mapping", "lesson_order": 1, "concepts_covered": ["enabler_001"], "estimated_duration_minutes": 45, "prerequisites": [], "mastery_criteria": "Can identify personal growth-resistant patterns"}, {"lesson_id": "lesson_01_02", "lesson_name": "Time Management Fundamentals", "lesson_order": 2, "concepts_covered": ["enabler_002", "enabler_003"], "estimated_duration_minutes": 90, "prerequisites": ["lesson_01_01"], "mastery_criteria": "Can effectively use timeboxing and prioritization"}, {"lesson_id": "lesson_01_03", "lesson_name": "Overcoming Resistance", "lesson_order": 3, "concepts_covered": ["enabler_004"], "estimated_duration_minutes": 60, "prerequisites": ["lesson_01_01"], "mastery_criteria": "Can apply MVGs to overcome procrastination"}, {"lesson_id": "lesson_01_04", "lesson_name": "Energy Optimization", "lesson_order": 4, "concepts_covered": ["enabler_005"], "estimated_duration_minutes": 75, "prerequisites": ["lesson_01_02"], "mastery_criteria": "Has identified and scheduled around energy patterns"}]}], "progression_logic": {"concept_name": "3-Phase Concurrent Progression Model", "summary": "The coach operates on concurrent phase logic - user works on all three phases but with emphasis shifting based on main focus and skill development.", "main_focus_guidance": ["Enablers Focus: Emphasize time management, focus, procrastination solutions", "Retrieval Focus: Emphasize memory techniques, active recall, spaced repetition", "Encoding Focus: Emphasize deep understanding, concept mapping, inquiry-based learning"], "advancement_criteria": ["Enablers → Retrieval: Consistent schedule, reduced procrastination, optimized environment", "Retrieval → Encoding: Strong memory techniques, can teach concepts, creates good questions", "Within phases: Technique mastery, confidence scores stable, ready for more challenge"]}, "keywords": ["curriculum", "iCanStudy", "learning_system", "progression", "mastery"], "additional_context": "This curriculum follows the concurrent phase model where all three phases are practiced together, but with emphasis shifting based on the learner's main focus and skill development."}]