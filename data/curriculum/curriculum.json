[{"curriculum_id": "ics_main_course", "curriculum_name": "iCanStudy Complete Learning System", "curriculum_type": "course", "summary": "Comprehensive learning methodology covering all three phases of skill development with structured progression.", "ics_phase": "curriculum", "total_estimated_hours": 40, "modules": [{"module_id": "module_01_enablers", "module_name": "Phase 1: Enablers - Building Your Foundation", "module_order": 1, "estimated_hours": 15, "description": "Establish robust self-management systems and overcome common learning obstacles", "learning_objectives": ["Master time management and planning techniques", "Develop awareness of growth-limiting habits", "Build consistent practice routines", "Overcome procrastination and resistance"], "lessons": [{"lesson_id": "lesson_01_01", "lesson_name": "Growth Habit Mapping", "lesson_order": 1, "concepts_covered": ["enabler_001"], "estimated_duration_minutes": 45, "prerequisites": [], "mastery_criteria": "Can identify personal growth-resistant patterns"}, {"lesson_id": "lesson_01_02", "lesson_name": "Time Management Fundamentals", "lesson_order": 2, "concepts_covered": ["enabler_002", "enabler_003"], "estimated_duration_minutes": 90, "prerequisites": ["lesson_01_01"], "mastery_criteria": "Can effectively use timeboxing and prioritization"}, {"lesson_id": "lesson_01_03", "lesson_name": "Overcoming Resistance", "lesson_order": 3, "concepts_covered": ["enabler_004"], "estimated_duration_minutes": 60, "prerequisites": ["lesson_01_01"], "mastery_criteria": "Can apply MVGs to overcome procrastination"}, {"lesson_id": "lesson_01_04", "lesson_name": "Energy Optimization", "lesson_order": 4, "concepts_covered": ["enabler_005"], "estimated_duration_minutes": 75, "prerequisites": ["lesson_01_02"], "mastery_criteria": "Has identified and scheduled around energy patterns"}]}, {"module_id": "module_02_retrieval", "module_name": "Phase 2: Retrieval - Strengthening Memory", "module_order": 2, "estimated_hours": 12, "description": "Master active learning techniques that strengthen memory and understanding", "learning_objectives": ["Implement active recall strategies", "Design effective spaced repetition systems", "Use practice testing for learning", "Apply interleaving for better retention"], "lessons": [{"lesson_id": "lesson_02_01", "lesson_name": "Active Recall Mastery", "lesson_order": 1, "concepts_covered": ["retrieval_001"], "estimated_duration_minutes": 60, "prerequisites": ["lesson_01_02"], "mastery_criteria": "Can effectively retrieve information without cues"}, {"lesson_id": "lesson_02_02", "lesson_name": "Spaced Repetition Systems", "lesson_order": 2, "concepts_covered": ["retrieval_002"], "estimated_duration_minutes": 90, "prerequisites": ["lesson_02_01"], "mastery_criteria": "Has implemented personal spaced repetition schedule"}, {"lesson_id": "lesson_02_03", "lesson_name": "Practice Testing Strategies", "lesson_order": 3, "concepts_covered": ["retrieval_003"], "estimated_duration_minutes": 75, "prerequisites": ["lesson_02_01"], "mastery_criteria": "Can create and use effective practice tests"}, {"lesson_id": "lesson_02_04", "lesson_name": "Advanced Retrieval Techniques", "lesson_order": 4, "concepts_covered": ["retrieval_004", "retrieval_005"], "estimated_duration_minutes": 105, "prerequisites": ["lesson_02_02", "lesson_02_03"], "mastery_criteria": "Can apply elaborative interrogation and interleaving"}]}, {"module_id": "module_03_encoding", "module_name": "Phase 3: Encoding - Deep Understanding", "module_order": 3, "estimated_hours": 13, "description": "Develop sophisticated thinking and understanding through advanced encoding techniques", "learning_objectives": ["Master explanation and teaching techniques", "Create effective visual knowledge representations", "Build analogical reasoning skills", "Integrate multiple representation systems"], "lessons": [{"lesson_id": "lesson_03_01", "lesson_name": "The Feynman Technique", "lesson_order": 1, "concepts_covered": ["encoding_001"], "estimated_duration_minutes": 90, "prerequisites": ["lesson_02_01"], "mastery_criteria": "Can explain complex concepts in simple terms"}, {"lesson_id": "lesson_03_02", "lesson_name": "Visual Knowledge Mapping", "lesson_order": 2, "concepts_covered": ["encoding_002"], "estimated_duration_minutes": 105, "prerequisites": ["lesson_03_01"], "mastery_criteria": "Can create comprehensive concept maps"}, {"lesson_id": "lesson_03_03", "lesson_name": "Analogical Reasoning", "lesson_order": 3, "concepts_covered": ["encoding_003"], "estimated_duration_minutes": 75, "prerequisites": ["lesson_03_01"], "mastery_criteria": "Can generate and use effective analogies for learning"}, {"lesson_id": "lesson_03_04", "lesson_name": "Multi-Modal Learning", "lesson_order": 4, "concepts_covered": ["encoding_004", "encoding_005"], "estimated_duration_minutes": 120, "prerequisites": ["lesson_03_02", "lesson_03_03"], "mastery_criteria": "Can integrate verbal, visual, and experiential learning"}]}], "keywords": ["curriculum", "iCanStudy", "learning_system", "progression", "mastery"], "additional_context": "This curriculum follows the concurrent phase model where all three phases are practiced together, but with emphasis shifting based on the learner's main focus and skill development."}]