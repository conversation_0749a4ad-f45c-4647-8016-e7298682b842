#!/usr/bin/env python3
"""
AI Learning Coach - Simplified Version Using Coach Brain Architecture
Dramatically reduced from 2283 lines to ~500 lines by moving intelligence to Coach Brain JSON files.
"""

import os
import json
import sqlite3
import datetime
import warnings
import google.generativeai as genai
from dotenv import load_dotenv

# Suppress warnings for clean interface
warnings.filterwarnings("ignore")

# Configuration
DB_PATH = "db/user_memory.db"
KNOWLEDGE_BASE_DIR = "knowledge_data"
COACH_BRAIN_DIR = "Coach_Brain"

class CoachBrain:
    """Loads and manages the strategic coaching intelligence from JSON files."""
    
    def __init__(self):
        self.strategic_brain = self.load_brain_file("coach_brain.json")
        self.curriculum_guidance = self.load_brain_file("curriculum_guidance.json")
    
    def load_brain_file(self, filename):
        """Load a brain file from the Coach_Brain directory."""
        try:
            path = os.path.join(COACH_BRAIN_DIR, filename)
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Warning: {filename} not found")
            return []
    
    def get_scenario_handler(self, user_input):
        """Find relevant scenario handler based on user input."""
        user_input_lower = user_input.lower()
        
        for item in self.strategic_brain:
            if item.get('concept_type') == 'ScenarioHandler':
                trigger_keywords = item.get('trigger_keywords', [])
                if any(keyword in user_input_lower for keyword in trigger_keywords):
                    return item
        return None
    
    def get_conversation_analysis(self, user_input):
        """Analyze conversation for patterns and information extraction."""
        analysis_results = []
        user_input_lower = user_input.lower()
        
        for item in self.strategic_brain:
            if item.get('concept_type') == 'ConversationAnalysis':
                patterns = item.get('detection_patterns', [])
                for pattern in patterns:
                    # Simple pattern matching - could be enhanced with regex
                    if any(word in user_input_lower for word in pattern.lower().split()):
                        analysis_results.append(item)
                        break
        
        return analysis_results
    
    def get_archetype_strategy(self, archetype):
        """Get coaching strategy for specific user archetype."""
        for item in self.strategic_brain:
            if item.get('concept_type') == 'ArchetypeStrategy':
                if archetype.lower() in item.get('concept_name', '').lower():
                    return item
        return None

class KnowledgeSearch:
    """Improved knowledge search that finds specific concepts accurately."""
    
    def __init__(self, knowledge_dir):
        self.knowledge_dir = knowledge_dir
    
    def search(self, query):
        """Search for concepts matching the query."""
        query_lower = query.lower()
        matches = []
        
        for filename in os.listdir(self.knowledge_dir):
            if filename.endswith('.json'):
                filepath = os.path.join(self.knowledge_dir, filename)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        for item in data:
                            if self._matches_query(item, query_lower):
                                matches.append(self._format_concept(item, filename))
                except Exception:
                    continue
        
        return matches[:3]  # Return top 3 matches
    
    def _matches_query(self, item, query_lower):
        """Check if item matches the query."""
        # Check concept name
        concept_name = item.get('concept_name', '').lower()
        if any(term in concept_name for term in query_lower.split()):
            return True
        
        # Check keywords
        keywords = item.get('keywords', [])
        if isinstance(keywords, list):
            for keyword in keywords:
                if keyword.lower() in query_lower:
                    return True
        
        return False
    
    def _format_concept(self, concept, filename):
        """Format concept for display."""
        return f"""**{concept.get('concept_name', 'Unknown')}** (from {filename})
Phase: {concept.get('ics_phase', 'Unknown')}
Summary: {concept.get('summary', 'No summary available')}
Instructions: {' '.join(concept.get('instructions', []))}"""

class DatabaseManager:
    """Simplified database operations."""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.setup_database()
    
    def setup_database(self):
        """Create database and tables if they don't exist."""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Essential tables only - full schema in original file
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS User_Profile (
                user_id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                archetype TEXT DEFAULT 'Unchained',
                current_main_focus TEXT DEFAULT 'Enablers',
                daily_study_goal_minutes INTEGER DEFAULT 120,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS Practice_Log (
                log_id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                skill_name TEXT NOT NULL,
                duration_minutes INTEGER NOT NULL,
                notes TEXT,
                log_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS Distraction_Habits (
                habit_id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                description TEXT NOT NULL,
                status TEXT DEFAULT 'Identified',
                impact_level INTEGER DEFAULT 3,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create default user if none exists
        cursor.execute("SELECT COUNT(*) FROM User_Profile")
        if cursor.fetchone()[0] == 0:
            cursor.execute("INSERT INTO User_Profile (user_id, name) VALUES (1, 'Jorge')")
        
        conn.commit()
        conn.close()
    
    def get_user_context(self):
        """Get user context for coaching."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name, archetype, current_main_focus FROM User_Profile WHERE user_id = 1")
        user_data = cursor.fetchone()
        
        cursor.execute("SELECT COUNT(*) FROM Practice_Log WHERE user_id = 1")
        practice_count = cursor.fetchone()[0]
        
        conn.close()
        
        if user_data:
            name, archetype, focus = user_data
            return f"User: {name}, Archetype: {archetype}, Focus: {focus}, Practice Sessions: {practice_count}"
        return "User context not available"
    
    def log_practice(self, skill_name, duration, notes=""):
        """Log a practice session."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO Practice_Log (user_id, skill_name, duration_minutes, notes)
            VALUES (1, ?, ?, ?)
        """, (skill_name, duration, notes))
        conn.commit()
        conn.close()

class AICoach:
    """Main AI Coach using the Coach Brain architecture."""
    
    def __init__(self):
        self.brain = CoachBrain()
        self.knowledge = KnowledgeSearch(KNOWLEDGE_BASE_DIR)
        self.db = DatabaseManager(DB_PATH)
        self.setup_ai()
    
    def setup_ai(self):
        """Configure the AI model."""
        load_dotenv()
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            raise ValueError("GOOGLE_API_KEY not found in .env file")
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
    
    def respond(self, user_input):
        """Generate intelligent coaching response using Coach Brain."""
        # Get user context
        user_context = self.db.get_user_context()
        
        # Analyze conversation for patterns
        conversation_analysis = self.brain.get_conversation_analysis(user_input)
        
        # Find relevant scenario handler
        scenario_handler = self.brain.get_scenario_handler(user_input)
        
        # Search knowledge base
        relevant_knowledge = self.knowledge.search(user_input)
        
        # Auto-detect and log practice sessions
        self._auto_detect_practice(user_input)
        
        # Build intelligent prompt
        prompt = self._build_coaching_prompt(
            user_input, user_context, scenario_handler, 
            conversation_analysis, relevant_knowledge
        )
        
        # Generate response
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"I'm having trouble thinking right now. Error: {e}"
    
    def _auto_detect_practice(self, user_input):
        """Auto-detect practice sessions from conversation."""
        import re
        
        # Simple patterns for practice detection
        patterns = [
            r"practiced (\w+) for (\d+) minutes",
            r"studied (\w+) for (\d+) minutes", 
            r"spent (\d+) minutes (?:working on|doing) (\w+)"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, user_input.lower())
            if match:
                if "minutes" in pattern:
                    if pattern.startswith(r"spent"):
                        duration, skill = match.groups()
                    else:
                        skill, duration = match.groups()
                    self.db.log_practice(skill, int(duration), "Auto-detected from conversation")
                    return f"📝 I noticed you practiced {skill} for {duration} minutes - logged automatically!"
        
        return None
    
    def _build_coaching_prompt(self, user_input, user_context, scenario_handler, analysis, knowledge):
        """Build the coaching prompt using Coach Brain intelligence."""
        
        prompt = f"""You are an intelligent AI Learning Coach using the iCanStudy methodology.

COACH BRAIN GUIDANCE:
{self._format_brain_guidance(scenario_handler, analysis)}

CURRENT SITUATION:
Time: {datetime.datetime.now().strftime('%A, %B %d, %Y at %I:%M %p')}
User says: "{user_input}"

USER CONTEXT:
{user_context}

RELEVANT KNOWLEDGE:
{chr(10).join(knowledge) if knowledge else "No specific knowledge found"}

INSTRUCTIONS:
Use your Coach Brain to analyze this situation and provide expert coaching. Be intelligent, personalized, and actionable.

Your response:"""
        
        return prompt
    
    def _format_brain_guidance(self, scenario_handler, analysis):
        """Format Coach Brain guidance for the prompt."""
        guidance = []
        
        if scenario_handler:
            guidance.append(f"SCENARIO: {scenario_handler['concept_name']}")
            guidance.append(f"Diagnosis: {scenario_handler['user_state_diagnosis']}")
            guidance.append(f"Objective: {scenario_handler['coach_objective']}")
        
        if analysis:
            guidance.append("CONVERSATION ANALYSIS:")
            for item in analysis:
                guidance.append(f"- {item['concept_name']}: {item['summary']}")
        
        return "\n".join(guidance) if guidance else "Apply general coaching principles"

def main():
    """Main application loop."""
    print("🎯 AI Learning Coach - Simplified Version")
    print("💬 Talk naturally - I'll understand and help you learn effectively")
    print("🚪 Type 'quit' to exit\n")
    
    coach = AICoach()
    
    # Time-based greeting
    now = datetime.datetime.now()
    if now.hour >= 23 or now.hour <= 5:
        print("🤖 Coach: ⚠️ It's very late! You should be sleeping for better learning tomorrow.")
    else:
        print("🤖 Coach: Hello! I'm your AI learning coach. What would you like to work on today?")
    
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print("🤖 Coach: Great session! Keep building those learning habits. See you next time!")
                break
            
            if not user_input:
                continue
            
            # Auto-detect practice first
            practice_detection = coach._auto_detect_practice(user_input)
            if practice_detection:
                print(practice_detection)
            
            # Generate coaching response
            response = coach.respond(user_input)
            print(f"\n🤖 Coach: {response}")
            
        except KeyboardInterrupt:
            print("\n🤖 Coach: Goodbye! Keep learning!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    main()
