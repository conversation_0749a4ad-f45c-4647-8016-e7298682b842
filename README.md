# AI Coach - Enhanced Active Learning System

An intelligent, proactive AI coach specialized for academic learning with focus on Computational Linguistics and Phonetics/Linguistics. The coach actively monitors your progress, enforces the 3-pillar learning system, and provides specialized guidance based on your academic field.

## 🚀 New Features (Enhanced Version)

### Active Coaching
- **Proactive Monitoring**: Continuously monitors your progress and initiates check-ins
- **Time-Based Interventions**: Smart notifications based on optimal learning windows
- **Pattern Recognition**: Automatically detects and addresses learning obstacles

### Academic Specialization
- **Computational Linguistics**: Specialized guidance for programming + linguistics
- **Phonetics/Linguistics**: Targeted support for auditory skills and transcription
- **Context-Aware Responses**: Adapts coaching based on detected academic context

### 3-Pillar System Enforcement
- **Technique Limiting**: Maximum 3 concurrent techniques (1 per pillar)
- **Progressive Mastery**: Ensures foundation before advancing to complex techniques
- **Intelligent Progression**: Guides through proper course sequence

### Enhanced Data Organization
- **Structured Knowledge Base**: Organized by curriculum, techniques, and coaching intelligence
- **Academic Profiles**: Specialized learning patterns for different fields
- **Scalable Architecture**: Supports 1000+ JSON imports and growing knowledge base

## 📁 Project Structure

```
ics/
├── active_coach.py              # 🆕 Enhanced active coaching script
├── coach.py                     # Original coaching script (preserved)
├── migrate_data_structure.py    # 🆕 Data organization migration tool
├── data/                        # 🆕 Organized data structure
│   ├── curriculum/              # Course modules and progression
│   ├── techniques/              # Learning techniques by pillar
│   ├── coaching_brain/          # Strategic coaching intelligence
│   ├── user_profiles/           # Academic specializations
│   └── app_guidance/            # Usage instructions
├── knowledge_data/              # Original knowledge files (preserved)
├── Coach_Brain/                 # Original coaching brain (preserved)
└── db/                          # User data and vector database
```

## 🎯 Quick Start

### Option 1: Enhanced Active Coach (Recommended)
```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Set up API key in .env file
echo "GOOGLE_API_KEY=your_api_key_here" > .env

# 3. Migrate to new data structure (optional but recommended)
python migrate_data_structure.py

# 4. Run the enhanced active coach
python active_coach.py
```

### Option 2: Original Coach (Backward Compatible)
```bash
python coach.py
```

## 🧠 Academic Specializations

### Computational Linguistics
- **Keywords Detected**: programming, algorithm, python, nlp, machine learning, syntax, parsing
- **Specialized Guidance**:
  - Separate coding and theory study blocks
  - Algorithm complexity active recall
  - Concept mapping for system architectures

### Phonetics & Linguistics
- **Keywords Detected**: phonetics, phonology, ipa, transcription, acoustic, spanish, romance
- **Specialized Guidance**:
  - Daily auditory discrimination practice
  - IPA symbol spaced repetition
  - Cross-linguistic comparison frameworks

## ⚡ 3-Pillar System

The coach enforces the iCanStudy 3-pillar concurrent learning model:

| Pillar | Focus | Max Techniques | Example Techniques |
|--------|-------|----------------|-------------------|
| **Enablers** | Foundation & Self-Management | 1 | Strategic Scheduling, MVG, Environment Design |
| **Retrieval** | Memory & Active Learning | 1 | Spaced Repetition, Active Recall, Practice Testing |
| **Encoding** | Deep Understanding | 1 | Feynman Technique, Concept Mapping, Analogies |

**Total Maximum**: 3 concurrent techniques across all pillars

## 🤖 Active Coaching Features

### Proactive Triggers
- **Morning Check-ins** (6-10 AM): Goal setting and energy optimization
- **Late Night Warnings** (11 PM-5 AM): Sleep guidance and memory consolidation
- **Practice Gap Detection**: Automatic reminders after 3+ days without practice
- **Pattern Recognition**: Identifies recurring distractions and obstacles

### Intelligent Responses
- **Academic Context Detection**: Automatically recognizes your field of study
- **Technique Enforcement**: Prevents overloading with too many techniques
- **Course Progression**: Guides through proper learning sequence
- **Specialized Guidance**: Provides field-specific learning strategies

## 📊 System Requirements

- Python 3.8+
- Google AI API key (Gemini model)
- 100MB free disk space
- Internet connection for AI responses

## 🔒 Data Privacy

- **Local Storage**: All personal data stays on your computer
- **No Cloud Sync**: Your progress never leaves your device
- **API Usage**: Only current conversation sent to Google AI
- **Full Control**: Delete `db/` folder to remove all stored data

## 💬 Usage Examples

### Basic Interaction
```
👤 You: I'm struggling with learning algorithms
🤖 Coach: I can see you're working on computational linguistics! For algorithm learning, let's start with a solid foundation. Do you have a consistent study schedule set up?
```

### Academic Context Detection
```
👤 You: I need to practice IPA transcription
🤖 Coach: Perfect! For phonetics work, I recommend daily 15-minute listening sessions. Have you set up a quiet environment optimized for auditory work?
```

### 3-Pillar Enforcement
```
👤 You: Can you teach me concept mapping and spaced repetition and time management?
🤖 Coach: I love your enthusiasm! However, you're asking about 3 techniques across all pillars. Let's focus on mastering one at a time. Which is your biggest challenge right now?
```

## 🛠 Advanced Features

### Custom Academic Profiles
Add your own academic specializations by editing `data/user_profiles/academic_specializations.json`

### Technique Customization
Modify learning techniques in `data/techniques/` organized by pillar

### Coaching Intelligence
Enhance coaching responses by editing `data/coaching_brain/strategic_brain.json`

## 🔧 Troubleshooting

### Common Issues
1. **API Key Error**: Ensure `.env` file contains valid Google API key
2. **Database Issues**: Delete `db/` folder to reset all data
3. **Import Errors**: Run `pip install -r requirements.txt` again

### Getting Help
- Check `Documentation.md` for detailed technical information
- Review `data/migration_summary.json` for recent changes
- Examine log files in the terminal for error details

## 🚀 Future Enhancements

- [ ] Web interface for easier interaction
- [ ] Mobile app companion
- [ ] Integration with calendar applications
- [ ] Advanced analytics dashboard
- [ ] Multi-language support
- [ ] Voice interaction capabilities

## 📝 Contributing

This is a personal learning coach project. Feel free to fork and adapt for your own academic needs!

## 📄 License

Personal use project - adapt as needed for your learning journey.