import os
import sqlite3
import json
import datetime
import chromadb
import google.generativeai as genai
from dotenv import load_dotenv
import warnings
import re

# Suppress deprecation warnings for cleaner interface
warnings.filterwarnings("ignore", category=DeprecationWarning)

# --- CONFIGURATION AND CONSTANTS ---
load_dotenv()

DB_PATH = "db/user_memory.db"
KNOWLEDGE_BASE_DIR = "knowledge_data"
CHROMA_DB_PATH = "db/knowledge_vectordb"
KNOWLEDGE_BASE_COLLECTION_NAME = "ics_knowledge_base"


# --- SYSTEM INITIALIZATION ---

def setup_directories():
    """Create necessary directories if they don't exist."""
    os.makedirs(KNOWLEDGE_BASE_DIR, exist_ok=True)
    os.makedirs("db", exist_ok=True)
    os.makedirs(CHROMA_DB_PATH, exist_ok=True)


def create_db_tables(conn):
    """Create the comprehensive SQLite database tables for advanced coaching."""
    cursor = conn.cursor()

    # User_Profile: Comprehensive user information and coaching state
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS User_Profile (
            user_id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            start_date DATE DEFAULT CURRENT_DATE,
            archetype TEXT DEFAULT 'Unchained',
            current_main_focus TEXT DEFAULT 'Enablers',
            timezone TEXT DEFAULT 'UTC',
            preferred_session_length INTEGER DEFAULT 25,
            daily_study_goal_minutes INTEGER DEFAULT 120,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_active DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Goals: User's learning objectives and targets
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Goals (
            goal_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            description TEXT NOT NULL,
            is_long_term BOOLEAN DEFAULT 0,
            target_date DATE,
            status TEXT DEFAULT 'Active',
            priority_level INTEGER DEFAULT 3,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    # Skills: Detailed competence tracking linked to knowledge base
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Skills (
            skill_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            concept_id TEXT NOT NULL,
            competence_level TEXT DEFAULT 'Unknown',
            last_practiced_date DATE,
            practice_count INTEGER DEFAULT 0,
            is_priority BOOLEAN DEFAULT 0,
            confidence_score REAL DEFAULT 0.0,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    # Practice_Log: Detailed session tracking with user feedback
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Practice_Log (
            log_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            skill_id INTEGER,
            log_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            duration_minutes INTEGER NOT NULL,
            user_notes_difficulty TEXT,
            confidence_score INTEGER CHECK (confidence_score >= 1 AND confidence_score <= 5),
            coach_intervention_id TEXT,
            session_type TEXT DEFAULT 'practice',
            effectiveness_rating INTEGER CHECK (effectiveness_rating >= 1 AND effectiveness_rating <= 5),
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id),
            FOREIGN KEY (skill_id) REFERENCES Skills (skill_id)
        )
    ''')

    # Distraction_Habits: Comprehensive habit and obstacle tracking
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Distraction_Habits (
            habit_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            description TEXT NOT NULL,
            trigger_description TEXT,
            mitigation_strategy_id TEXT,
            status TEXT DEFAULT 'Identified',
            frequency TEXT DEFAULT 'Unknown',
            impact_level INTEGER DEFAULT 3 CHECK (impact_level >= 1 AND impact_level <= 5),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_occurred DATETIME,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    # Scheduled_Events: Comprehensive calendar and planning system
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Scheduled_Events (
            event_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            event_description TEXT NOT NULL,
            event_type TEXT NOT NULL,
            scheduled_start_time DATETIME NOT NULL,
            scheduled_end_time DATETIME NOT NULL,
            is_completed BOOLEAN DEFAULT 0,
            is_recurring BOOLEAN DEFAULT 0,
            recurrence_pattern TEXT,
            related_skill_id INTEGER,
            related_goal_id INTEGER,
            priority_level INTEGER DEFAULT 3,
            location TEXT,
            preparation_time_minutes INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id),
            FOREIGN KEY (related_skill_id) REFERENCES Skills (skill_id),
            FOREIGN KEY (related_goal_id) REFERENCES Goals (goal_id)
        )
    ''')

    # Coach_Interactions: Track coaching conversations and interventions
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Coach_Interactions (
            interaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            interaction_type TEXT NOT NULL,
            trigger_reason TEXT,
            coach_message TEXT NOT NULL,
            user_response TEXT,
            concepts_referenced TEXT,
            effectiveness_rating INTEGER CHECK (effectiveness_rating >= 1 AND effectiveness_rating <= 5),
            follow_up_needed BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    # User_Assessment: Periodic assessments and roadmap updates
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS User_Assessment (
            assessment_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            assessment_type TEXT NOT NULL,
            overall_progress_score REAL,
            main_strengths TEXT,
            main_weaknesses TEXT,
            recommended_focus_areas TEXT,
            roadmap_adjustments TEXT,
            next_assessment_date DATE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    # Session_Logs: Track daily interaction patterns
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Session_Logs (
            session_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            session_date DATE NOT NULL,
            session_start_time DATETIME,
            session_end_time DATETIME,
            total_interactions INTEGER DEFAULT 0,
            main_topics_discussed TEXT,
            problems_identified TEXT,
            solutions_suggested TEXT,
            user_mood_indicators TEXT,
            effectiveness_score REAL,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    # Pattern_Analysis: Store identified behavioral patterns
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Pattern_Analysis (
            pattern_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            pattern_type TEXT NOT NULL,
            pattern_description TEXT NOT NULL,
            frequency TEXT NOT NULL,
            trigger_conditions TEXT,
            impact_assessment TEXT,
            suggested_interventions TEXT,
            pattern_strength REAL,
            first_detected DATE,
            last_observed DATE,
            status TEXT DEFAULT 'active',
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    # Course_Progress: Track progress through course modules
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Course_Progress (
            progress_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            current_module TEXT DEFAULT 'module_01',
            module_status TEXT DEFAULT 'in_progress',
            completion_percentage REAL DEFAULT 0.0,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    conn.commit()
    print("Advanced database schema with pattern recognition created successfully.")


def setup_database():
    """Initializes the SQLite database and its tables."""
    conn = sqlite3.connect(DB_PATH)
    create_db_tables(conn)

    # Check if user profile exists, if not, create one.
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM User_Profile WHERE user_id = 1")
    if cursor.fetchone() is None:
        print("Welcome to your AI Learning Coach!")
        print("Let's set up your profile for personalized coaching.")

        name = input("What is your name? ")

        print("\nWhat's your main learning goal right now?")
        goal_description = input("Goal: ")

        print("\nHow many minutes per day do you want to dedicate to focused learning?")
        daily_goal = input("Daily goal (default 120 minutes): ") or "120"

        # Create user profile
        cursor.execute("""
            INSERT INTO User_Profile (user_id, name, daily_study_goal_minutes)
            VALUES (?, ?, ?)
        """, (1, name, int(daily_goal)))

        # Create initial goal
        cursor.execute("""
            INSERT INTO Goals (user_id, description, is_long_term)
            VALUES (?, ?, ?)
        """, (1, goal_description, True))

        # Initialize course progress
        cursor.execute("""
            INSERT INTO Course_Progress (user_id, current_module)
            VALUES (?, ?)
        """, (1, 'module_01'))

        conn.commit()
        print(f"\nWelcome, {name}! Your coaching profile has been created.")
        print("I'll help you achieve your learning goals through personalized guidance.")

        # Trigger comprehensive assessment
        print("\nNow I need to understand you better to provide effective coaching...")
        conduct_initial_assessment()

    conn.close()


def conduct_initial_assessment():
    """Conducts comprehensive iCanStudy-aligned assessment to understand the user."""
    print("\n" + "="*70)
    print("iCANSTUDY COACHING ASSESSMENT")
    print("="*70)
    print("To be a world-class coach, I need to understand your current learning system.")
    print("This diagnostic will take 5-10 minutes but will transform your coaching experience.")
    print("Please answer honestly - there are no 'right' answers, only insights.\n")

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # === SECTION 1: ENABLERS (Your Foundation) ===
    print("SECTION 1: YOUR LEARNING FOUNDATION (Enablers)")
    print("-" * 50)

    # Critical enabler: Schedule system
    has_schedule = input(
        "1. Do you use a calendar to plan your study week? (yes/no): ").lower().startswith('y')

    # Urgency trap assessment
    print("\n2. Imagine you have two tasks:")
    print("   (A) A small assignment due tomorrow worth 5% of your grade")
    print("   (B) A 1-hour study session for your final exam in 2 months")
    prioritization = input(
        "   Which do you almost always do first? (A/B): ").upper().strip()

    # Procrastination root cause
    print("\n3. When you procrastinate on a big task, what's the usual feeling behind it?")
    print("   a) The task feels too big and I don't know where to start")
    print("   b) I'm afraid I won't do it perfectly")
    print("   c) It just feels boring and I lack motivation")
    print("   d) I feel too tired or drained to begin")
    procrastination_reason = input(
        "   Your answer (a/b/c/d): ").lower().strip()

    # Environment assessment
    print("\n4. When you study, is your phone:")
    print("   a) In another room")
    print("   b) On your desk face down")
    print("   c) On and next to you")
    phone_environment = input("   Your answer (a/b/c): ").lower().strip()

    # === SECTION 2: RETRIEVAL (Your Learning Process) ===
    print("\nSECTION 2: YOUR CURRENT LEARNING PROCESS (Retrieval)")
    print("-" * 55)

    # Active vs passive review
    print("5. After learning something in a lecture, what does your first review session look like?")
    print("   a) I re-read my notes or the textbook chapter")
    print("   b) I try to explain the concepts out loud from memory")
    print("   c) I do practice questions immediately")
    review_method = input("   Your answer (a/b/c): ").lower().strip()

    # Pre-study habits
    print("\n6. Before you attend a new lecture or start a new topic, what do you typically do?")
    print("   a) Nothing, I learn it for the first time in the lecture")
    print("   b) I quickly skim the chapter headings and summary")
    print("   c) I try to read the whole chapter in detail")
    pre_study_habit = input("   Your answer (a/b/c): ").lower().strip()

    # === SECTION 3: MINDSET (The Core Problem) ===
    print("\nSECTION 3: YOUR MINDSET (The Most Critical Factor)")
    print("-" * 50)

    # Fear of failure assessment
    print("7. How do you feel when you get a practice question wrong?")
    print("   a) Great! I found a gap in my knowledge")
    print("   b) Annoyed, but it's part of the process")
    print("   c) I feel stupid and my confidence drops")
    failure_response = input("   Your answer (a/b/c): ").lower().strip()

    # Response to challenge (Growth Habit Mapping)
    print("\n8. When a learning method feels difficult and confusing at first, what is your first instinct?")
    print("   a) To push through and trust the process")
    print("   b) To look for an easier, more comfortable method")
    print("   c) To stop and assume the method doesn't work for me")
    challenge_response = input("   Your answer (a/b/c): ").lower().strip()

    # === SECTION 4: CURRENT CONTEXT ===
    print("\nSECTION 4: YOUR CURRENT SITUATION")
    print("-" * 35)

    academic_level = input(
        "9. What are you studying? (university subject, course, etc.): ")
    upcoming_deadlines = input(
        "10. Do you have any upcoming exams or major deadlines? When?: ")
    main_challenge = input(
        "11. What's your biggest frustration with learning right now?: ")

    # === ANALYSIS & LOGGING ===

    # Log critical habit if no schedule
    if not has_schedule:
        cursor.execute("""
            INSERT INTO Distraction_Habits (user_id, description, impact_level, status)
            VALUES (?, ?, ?, ?)
        """, (1, "Establish calendar/schedule system - CRITICAL ENABLER", 5, "Identified"))

    # Analyze and store comprehensive assessment data
    assessment_data = {
        "has_schedule": has_schedule,
        "prioritization": prioritization,
        "procrastination_reason": procrastination_reason,
        "phone_environment": phone_environment,
        "review_method": review_method,
        "pre_study_habit": pre_study_habit,
        "failure_response": failure_response,
        "challenge_response": challenge_response,
        "academic_level": academic_level,
        "upcoming_deadlines": upcoming_deadlines,
        "main_challenge": main_challenge
    }

    # Determine focus areas based on responses
    focus_areas = []
    weaknesses = []
    strengths = []

    # Enablers analysis
    if not has_schedule:
        focus_areas.append("Schedule system establishment")
        weaknesses.append("No calendar/planning system")
    if prioritization == "A":
        focus_areas.append("Urgency trap awareness")
        weaknesses.append("Falls into urgency trap")
    if phone_environment == "c":
        focus_areas.append("Environment optimization")
        weaknesses.append("Distracting study environment")

    # Retrieval analysis
    if review_method == "a":
        focus_areas.append("Active recall techniques")
        weaknesses.append("Relies on passive re-reading")
    elif review_method == "b":
        strengths.append("Uses active recall naturally")

    if pre_study_habit == "a":
        focus_areas.append("Pre-study preparation")
        weaknesses.append("No preparation before learning")

    # Mindset analysis (most critical)
    if failure_response == "c":
        focus_areas.append("Growth mindset development")
        weaknesses.append("Fixed mindset toward failure")
    elif failure_response == "a":
        strengths.append("Healthy relationship with failure")

    if challenge_response == "c":
        focus_areas.append("Resistance to difficulty")
        weaknesses.append("Avoids challenging methods")
    elif challenge_response == "a":
        strengths.append("Embraces challenging processes")

    cursor.execute("""
        INSERT INTO User_Assessment
        (user_id, assessment_type, main_weaknesses, main_strengths, recommended_focus_areas)
        VALUES (?, ?, ?, ?, ?)
    """, (1, 'initial_ics_aligned', json.dumps(assessment_data),
          json.dumps(strengths) if strengths else json.dumps(
              ["To be determined through observation"]),
          json.dumps(focus_areas) if focus_areas else json.dumps(["General skill development"])))

    conn.commit()
    conn.close()

    print("\n" + "="*70)
    print("ASSESSMENT COMPLETE!")
    print("="*70)
    print("Thank you for your honest responses. This gives me a much clearer picture.")
    print("I can now provide truly personalized coaching aligned with the iCanStudy methodology.")

    # Generate immediate personalized roadmap and action plan
    generate_post_assessment_roadmap(
        assessment_data, focus_areas, weaknesses, strengths)

    # Create and store structured learning roadmap in database
    create_structured_learning_roadmap(
        assessment_data, focus_areas, weaknesses)

    print("Let's start building your learning system step by step!\n")


def generate_post_assessment_roadmap(assessment_data, focus_areas, weaknesses, strengths):
    """Generates immediate personalized roadmap and action plan based on assessment."""
    print("\n" + "="*70)
    print("🎯 YOUR PERSONALIZED LEARNING ROADMAP")
    print("="*70)

    # Analyze critical issues first
    critical_issues = []
    if not assessment_data.get("has_schedule"):
        critical_issues.append("No calendar/planning system")
    if assessment_data.get("prioritization") == "A":
        critical_issues.append("Falls into urgency trap")
    if assessment_data.get("phone_environment") == "c":
        critical_issues.append("Distracting study environment")

    # Display critical issues that need immediate attention
    if critical_issues:
        print("\n🚨 CRITICAL FOUNDATIONS MISSING:")
        for i, issue in enumerate(critical_issues, 1):
            print(f"   {i}. {issue}")
        print("\n⚠️  These MUST be fixed before any other learning can be effective!")

    # Display assessment insights
    print(f"\n📊 ASSESSMENT INSIGHTS:")
    if strengths and strengths != ["To be determined through observation"]:
        print(f"✅ Your Strengths: {', '.join(strengths)}")
    if weaknesses:
        print(f"⚠️  Areas to Improve: {', '.join(weaknesses)}")

    # Generate specific action plan
    print(f"\n📋 YOUR IMMEDIATE ACTION PLAN:")

    # Phase 1: Critical Enablers (always first)
    if not assessment_data.get("has_schedule"):
        print("\n🎯 PHASE 1: ESTABLISH FOUNDATIONS (THIS WEEK)")
        print("   Priority 1: Set up your calendar system")
        print("   - Use Google Calendar, Apple Calendar, or any digital calendar")
        print("   - Block out ALL your classes and commitments")
        print("   - Schedule specific study blocks (don't leave it to chance)")
        print("   - This is NON-NEGOTIABLE for effective learning")

    # Phase 2: Address specific weaknesses
    if "passive re-reading" in str(weaknesses):
        print("\n🎯 PHASE 2: UPGRADE YOUR STUDY METHODS (NEXT 2 WEEKS)")
        print("   Priority 2: Replace passive re-reading with active recall")
        print("   - After reading, close your book and try to explain the concept")
        print("   - Test yourself from memory before checking answers")
        print("   - Use the Feynman Technique: explain it like you're teaching a child")

    if "fixed mindset" in str(weaknesses):
        print("\n🎯 PHASE 3: DEVELOP GROWTH MINDSET (ONGOING)")
        print("   Priority 3: Change your relationship with failure")
        print("   - When you get something wrong, say 'Great! I found a gap!'")
        print("   - View difficult methods as brain training, not obstacles")
        print("   - Practice Growth Habit Mapping to build awareness")

    # Provide specific next steps
    print(f"\n🚀 YOUR NEXT STEPS (RIGHT NOW):")
    if not assessment_data.get("has_schedule"):
        print("   1. Open your calendar app and block out this week's schedule")
        print("   2. Schedule your first 25-minute study block for tomorrow")
        print("   3. Come back and tell me: 'I set up my calendar'")
    else:
        print("   1. Try one 25-minute active recall session today")
        print("   2. Pick any topic you're learning and explain it from memory")
        print("   3. Come back and tell me how it went")

    # Set expectations for coaching relationship
    print(f"\n🤖 HOW I'LL COACH YOU:")
    print("   • I'll check on your progress and suggest next steps")
    print("   • I'll help you when you get stuck or distracted")
    print("   • I'll celebrate your wins and guide you through challenges")
    print("   • I'll gradually introduce more advanced techniques as you master basics")

    print(f"\n💬 Ready to start? Tell me what you want to work on first!")
    print("="*70)


def create_structured_learning_roadmap(assessment_data, focus_areas, weaknesses):
    """Creates and stores a structured learning roadmap based on course modules."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Create roadmap table if it doesn't exist
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS Learning_Roadmap (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            current_module TEXT,
            current_skills TEXT,
            next_actions TEXT,
            roadmap_data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)

    # Determine starting module based on assessment
    if not assessment_data.get("has_schedule"):
        current_module = "module_01"  # Rapid Start - Enablers
        current_skills = json.dumps({
            "enabler": "Schedule system establishment",
            "retrieval": None,
            "encoding": None
        })
        next_actions = json.dumps([
            "Set up calendar system",
            "Practice urgency vs importance distinction",
            "Create focused study environment"
        ])
    else:
        current_module = "module_02"  # Fundamentals 1 - SIR
        current_skills = json.dumps({
            "enabler": "Time management (established)",
            "retrieval": "Basic SIR implementation",
            "encoding": None
        })
        next_actions = json.dumps([
            "Implement spaced repetition schedule",
            "Practice active recall techniques",
            "Begin interleaving practice"
        ])

    # Store roadmap
    roadmap_data = {
        "assessment_weaknesses": weaknesses,
        "focus_areas": focus_areas,
        "max_concurrent_skills": 3,
        "skill_distribution": "1 enabler + 1 retrieval + 1 encoding (max)",
        "progression_rule": "Master enablers before advancing to complex techniques"
    }

    cursor.execute("""
        INSERT OR REPLACE INTO Learning_Roadmap
        (user_id, current_module, current_skills, next_actions, roadmap_data)
        VALUES (?, ?, ?, ?, ?)
    """, (1, current_module, current_skills, next_actions, json.dumps(roadmap_data)))

    conn.commit()
    conn.close()

    print(f"\n🗺️  STRUCTURED ROADMAP CREATED")
    print(f"📍 Starting Module: {current_module}")
    print(f"🎯 Current Focus: {json.loads(current_skills)}")
    print(f"📋 Next Actions: {json.loads(next_actions)}")


def get_current_roadmap_status():
    """Gets the user's current position in the learning roadmap."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("""
        SELECT current_module, current_skills, next_actions, roadmap_data
        FROM Learning_Roadmap
        WHERE user_id = 1
        ORDER BY updated_at DESC
        LIMIT 1
    """)

    result = cursor.fetchone()
    conn.close()

    if result:
        current_module, current_skills, next_actions, roadmap_data = result
        return {
            "current_module": current_module,
            "current_skills": json.loads(current_skills) if current_skills else {},
            "next_actions": json.loads(next_actions) if next_actions else [],
            "roadmap_data": json.loads(roadmap_data) if roadmap_data else {}
        }
    return None


def generate_active_coaching_guidance(user_input, roadmap_status, collection):
    """CORE ACTIVE COACHING: Generates guidance based on user's current roadmap position."""
    current_module = roadmap_status["current_module"]
    current_skills = roadmap_status["current_skills"]
    next_actions = roadmap_status["next_actions"]

    user_input_lower = user_input.lower()

    # Load course module data
    course_modules = load_course_modules()
    current_module_data = next(
        (m for m in course_modules if m["concept_id"] == current_module), None)

    if not current_module_data:
        return None

    # ACTIVE COACHING LOGIC: What should the coach do based on current state?

    # 1. If user reports completion of a next action
    if any(action.lower() in user_input_lower for action in next_actions):
        return handle_action_completion(user_input, roadmap_status, current_module_data, collection)

    # 2. If user asks for help with current module techniques
    if any(keyword in user_input_lower for keyword in current_module_data["keywords"]):
        return provide_technique_guidance(user_input, current_module_data, current_skills, collection)

    # 3. If user is struggling with something
    if any(word in user_input_lower for word in ['struggling', 'stuck', 'difficult', 'hard', 'confused']):
        return diagnose_and_solve_with_roadmap(user_input, roadmap_status, current_module_data, collection)

    # 4. If user mentions distraction/procrastination (always priority for enablers)
    if any(word in user_input_lower for word in ['distracted', 'procrastinating', 'chess', 'phone']):
        return handle_distraction_with_roadmap(user_input, roadmap_status, collection)

    # 5. If user asks general questions, guide them to current module focus
    if any(word in user_input_lower for word in ['how', 'what', 'should i', 'technique']):
        return guide_to_current_focus(user_input, roadmap_status, current_module_data, collection)

    # 6. Default: Proactively guide based on current module
    return proactive_module_guidance(roadmap_status, current_module_data, collection)


def load_course_modules():
    """Loads course modules from JSON file."""
    try:
        with open('/Users/<USER>/Documents/Projects/Code/python_scripts/ics/knowledge_data/course_modules.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return []


def handle_action_completion(user_input, roadmap_status, current_module_data, collection):
    """Handles when user reports completing a next action."""
    # Celebrate completion and advance to next step
    response = f"🎉 Excellent work! I can see you're making real progress. "

    # Determine next step based on current module
    if roadmap_status["current_module"] == "module_01":
        if "calendar" in user_input.lower():
            response += "Now that you have your calendar set up, let's work on the urgency trap. Can you identify one task you're doing that's urgent but not important?"
            # Update roadmap to next action
            update_roadmap_progress("urgency_trap_practice")
        elif "urgency" in user_input.lower():
            response += "Great! Now let's create your focused study environment. Where will you study, and how will you eliminate distractions?"
            update_roadmap_progress("environment_setup")
    elif roadmap_status["current_module"] == "module_02":
        if "spaced repetition" in user_input.lower():
            response += "Perfect! Now let's add interleaving to your practice. Try switching between explaining concepts, drawing mindmaps, and doing practice questions."
            update_roadmap_progress("interleaving_practice")

    return response


def provide_technique_guidance(user_input, current_module_data, current_skills, collection):
    """Provides specific technique guidance based on current module."""
    # Get relevant knowledge from collection
    relevant_concepts = query_knowledge_base(
        collection, user_input, n_results=2)

    response = f"Based on your current focus in {current_module_data['concept_name']}, here's what you need to know:\n\n"

    if relevant_concepts:
        response += relevant_concepts[0] + "\n\n"

    # Add specific instructions from current module
    response += "Your next step: " + current_module_data["instructions"][0]

    return response


def store_conversation_context(user_input, coach_response, roadmap_status):
    """Stores conversation context for continuity."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Create conversation context table if it doesn't exist
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS Conversation_Context (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            user_input TEXT,
            coach_response TEXT,
            roadmap_module TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)

    cursor.execute("""
        INSERT INTO Conversation_Context
        (user_id, user_input, coach_response, roadmap_module)
        VALUES (?, ?, ?, ?)
    """, (1, user_input, coach_response, roadmap_status["current_module"]))

    conn.commit()
    conn.close()


def update_roadmap_progress(next_action):
    """Updates the user's roadmap progress."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # This would update the roadmap with the new action
    # For now, we'll just log it
    cursor.execute("""
        INSERT INTO Session_Logs (user_id, session_date, main_topics_discussed)
        VALUES (?, date('now'), ?)
    """, (1, f"Completed action: {next_action}"))

    conn.commit()
    conn.close()


def diagnose_and_solve_with_roadmap(user_input, roadmap_status, current_module_data, collection):
    """Diagnoses user's problem and provides solution based on current roadmap."""
    # Get specific help based on current module
    if roadmap_status["current_module"] == "module_01":
        if "math" in user_input.lower() or "formula" in user_input.lower():
            return "I see you're struggling with math concepts. Since you're in the Enablers phase, let's first make sure you have a proper study schedule. Have you blocked out specific times for math practice in your calendar? Without a schedule, even the best techniques won't work."
        else:
            return f"I understand you're struggling. Since you're working on {current_module_data['concept_name']}, let's focus on the foundations first. {current_module_data['instructions'][0]} What specific part of this is challenging you?"

    elif roadmap_status["current_module"] == "module_02":
        if "math" in user_input.lower() or "formula" in user_input.lower():
            return "For math formulas, let's use active recall from your SIR training. Instead of re-reading formulas, try to write them from memory first, then check. Can you pick one formula and try explaining what each symbol means without looking?"

    return f"Based on your current focus in {current_module_data['concept_name']}, here's what I recommend: {current_module_data['instructions'][0]}"


def handle_distraction_with_roadmap(user_input, roadmap_status, collection):
    """Handles distraction issues based on current roadmap position."""
    if roadmap_status["current_module"] == "module_01":
        return "Distractions are exactly what we're working on in your current module! This is perfect timing. Let's implement the BEDS-M framework: put your phone in another room, set up a dedicated study space, and use your calendar to block focused study time. Which of these will you tackle first?"
    else:
        return "I notice you're getting distracted. Since you've moved past the Enablers phase, this suggests we need to revisit your foundations. Let's go back to your environment setup - is your phone still in another room during study time?"


def guide_to_current_focus(user_input, roadmap_status, current_module_data, collection):
    """Guides user to their current module focus."""
    return f"Great question! Since you're currently working on {current_module_data['concept_name']}, let's focus on that. Your priority right now is: {current_module_data['instructions'][0]} How can I help you with this specific step?"


def proactive_module_guidance(roadmap_status, current_module_data, collection):
    """Provides proactive guidance based on current module."""
    current_skills = roadmap_status["current_skills"]

    if roadmap_status["current_module"] == "module_01":
        if not current_skills.get("enabler"):
            return "Let's get your foundations solid! Your first priority is setting up a calendar system. Have you chosen which calendar app you'll use? This is non-negotiable for everything else to work."
        else:
            return "I see you're working on your enabler skills. How's your calendar system working? Are you successfully blocking out study time and sticking to it?"

    elif roadmap_status["current_module"] == "module_02":
        return "You're ready for active learning techniques! Let's start with spaced repetition. Pick any topic you're learning and schedule three review sessions: today, end of week, and end of month. What topic will you start with?"

    return f"You're working on {current_module_data['concept_name']}. Your next step: {current_module_data['instructions'][0]}"


def classify_user_message(user_input, user_context):
    """Classifies the user's message to determine appropriate coaching response."""
    user_input_lower = user_input.lower()

    # Check for direct questions about specific topics (math, subjects, etc.)
    if any(word in user_input_lower for word in ['math', 'calculus', 'physics', 'chemistry', 'biology', 'history', 'english']):
        if any(word in user_input_lower for word in ['struggle', 'struggling', 'difficult', 'hard', 'help']):
            return "subject_help"
        elif any(word in user_input_lower for word in ['topic', 'know', 'understand', 'learn']):
            return "subject_question"

    # Check for direct questions to the coach
    if any(phrase in user_input_lower for phrase in ['do you know', 'are you reading', 'hello', 'can you', 'are you']):
        return "direct_question"

    # Check for progress reports
    if any(word in user_input_lower for word in ['practiced', 'studied', 'worked on', 'completed', 'finished']):
        return "progress_report"

    # Check for struggles/challenges
    if any(word in user_input_lower for word in ['struggling', 'stuck', 'difficult', 'hard', 'confused', 'frustrated']):
        return "seeking_help"

    # Check for distraction mentions
    if any(word in user_input_lower for word in ['distracted', 'procrastinating', 'chess', 'phone', 'social media']):
        return "distraction_issue"

    # Check for questions about methods/techniques
    if any(word in user_input_lower for word in ['how', 'what', 'why', 'should i', 'best way', 'technique']):
        return "seeking_guidance"

    # Check for schedule/planning related
    if any(word in user_input_lower for word in ['schedule', 'calendar', 'plan', 'time', 'when']):
        return "planning_help"

    # Check for completion/success reports
    if any(word in user_input_lower for word in ['done', 'set up', 'created', 'established', 'ready']):
        return "completion_report"

    # Check for assessment-related responses
    if "calendar" in user_input_lower or "schedule" in user_input_lower:
        return "foundation_setup"

    # Default to general conversation
    return "general_conversation"


def get_coaching_strategy(message_type, user_input, user_context):
    """Determines the coaching strategy based on message type and context."""
    strategies = {
        "direct_question": {
            "approach": "direct_response",
            "focus": "answer the question directly, then provide relevant coaching",
            "tone": "responsive and helpful"
        },
        "subject_help": {
            "approach": "subject_specific_help",
            "focus": "provide specific help with the subject, integrate learning techniques",
            "tone": "supportive and educational"
        },
        "subject_question": {
            "approach": "subject_exploration",
            "focus": "explore the subject topic, connect to learning strategies",
            "tone": "curious and educational"
        },
        "progress_report": {
            "approach": "celebrate_and_guide",
            "focus": "acknowledge progress, ask for details, suggest next steps",
            "tone": "encouraging and forward-looking"
        },
        "seeking_help": {
            "approach": "diagnose_and_solve",
            "focus": "identify specific problem, provide targeted solution, check understanding",
            "tone": "supportive and solution-focused"
        },
        "distraction_issue": {
            "approach": "pattern_intervention",
            "focus": "acknowledge pattern, provide specific strategy, create accountability",
            "tone": "understanding but firm"
        },
        "seeking_guidance": {
            "approach": "teach_and_apply",
            "focus": "explain concept, provide practical steps, suggest practice",
            "tone": "educational and actionable"
        },
        "planning_help": {
            "approach": "structure_and_organize",
            "focus": "assess current state, provide planning framework, create specific plan",
            "tone": "systematic and practical"
        },
        "completion_report": {
            "approach": "validate_and_advance",
            "focus": "celebrate completion, verify understanding, introduce next challenge",
            "tone": "proud and progressive"
        },
        "foundation_setup": {
            "approach": "foundation_building",
            "focus": "ensure critical foundations are solid before advancing",
            "tone": "firm but supportive"
        },
        "general_conversation": {
            "approach": "assess_and_guide",
            "focus": "understand current state, identify needs, provide relevant guidance",
            "tone": "curious and helpful"
        }
    }

    return strategies.get(message_type, strategies["general_conversation"])


def load_knowledge_base_if_empty(collection):
    """Loads knowledge from JSON files into ChromaDB if the collection is empty."""
    if collection.count() > 0:
        print("Knowledge base is already loaded.")
        return

    print("Knowledge base is empty. Loading from JSON files...")
    documents = []
    metadatas = []
    ids = []

    for filename in os.listdir(KNOWLEDGE_BASE_DIR):
        if filename.endswith('.json'):
            filepath = os.path.join(KNOWLEDGE_BASE_DIR, filename)
            print(f"Loading {filename}...")
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                    # Handle different JSON structures
                    for i, item in enumerate(data):
                        doc_content, metadata, item_id = process_knowledge_item(
                            item, filename)
                        if doc_content and item_id:
                            # Make ID unique by adding filename prefix
                            unique_id = f"{filename.replace('.json', '')}_{item_id}"
                            documents.append(doc_content)
                            metadatas.append(metadata)
                            ids.append(unique_id)

            except Exception as e:
                print(f"Error loading {filename}: {e}")
                continue

    if ids:
        collection.add(documents=documents, metadatas=metadatas, ids=ids)
        print(
            f"Successfully loaded {len(ids)} concepts into the knowledge base.")


def process_knowledge_item(item, filename):
    """Processes a single knowledge item from JSON, handling different structures."""
    try:
        # Standard concept structure (most files)
        if 'concept_name' in item and 'concept_id' in item:
            doc_content = (
                f"Concept: {item['concept_name']}. "
                f"Summary: {item.get('summary', '')}. "
                f"Instructions: {' '.join(item.get('instructions', []))}. "
                f"Context: {item.get('additional_context', '')}"
            )
            metadata = {
                "concept_id": item['concept_id'],
                "concept_name": item['concept_name'],
                "ics_phase": item.get('ics_phase', 'unknown'),
                "source_file": filename
            }
            return doc_content, metadata, item['concept_id']

        # Curriculum structure
        elif 'curriculum_name' in item and 'curriculum_id' in item:
            doc_content = (
                f"Curriculum: {item['curriculum_name']}. "
                f"Summary: {item.get('summary', '')}. "
                f"Type: {item.get('curriculum_type', '')}. "
                f"Context: {item.get('additional_context', '')}"
            )
            metadata = {
                "concept_id": item['curriculum_id'],
                "concept_name": item['curriculum_name'],
                "ics_phase": item.get('ics_phase', 'curriculum'),
                "source_file": filename
            }
            return doc_content, metadata, item['curriculum_id']

        # Module structure (if different from concept)
        elif 'module_name' in item and 'module_id' in item:
            doc_content = (
                f"Module: {item['module_name']}. "
                f"Description: {item.get('description', '')}. "
                f"Objectives: {' '.join(item.get('learning_objectives', []))}. "
                f"Context: {item.get('additional_context', '')}"
            )
            metadata = {
                "concept_id": item['module_id'],
                "concept_name": item['module_name'],
                "ics_phase": item.get('ics_phase', 'module'),
                "source_file": filename
            }
            return doc_content, metadata, item['module_id']

        else:
            print(f"Unknown structure in {filename}: {list(item.keys())}")
            return None, None, None

    except Exception as e:
        print(f"Error processing item in {filename}: {e}")
        return None, None, None


def configure_gemini():
    """Configures the Gemini API."""
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        raise ValueError(
            "GOOGLE_API_KEY not found in .env file. Please set it.")
    genai.configure(api_key=api_key)


def initialize_system():
    """Runs all setup functions to prepare the application."""
    print("Initializing The Coach...")
    setup_directories()
    setup_database()

    # Setup ChromaDB
    chroma_client = chromadb.PersistentClient(path=CHROMA_DB_PATH)
    collection = chroma_client.get_or_create_collection(
        name=KNOWLEDGE_BASE_COLLECTION_NAME)
    load_knowledge_base_if_empty(collection)

    # Configure Gemini
    configure_gemini()

    print("Initialization complete. The Coach is ready.\n")
    return collection


# --- CORE COACH LOGIC ---

def query_knowledge_base(collection, query_text, n_results=3):
    """Queries the ChromaDB knowledge base for relevant concepts."""
    results = collection.query(query_texts=[query_text], n_results=n_results)
    return results['documents'][0] if results else []


def get_gemini_response(prompt):
    """Gets a response from the Gemini model."""
    model = genai.GenerativeModel('gemini-2.0-flash-lite')
    response = model.generate_content(prompt)
    return response.text


def get_user_context():
    """Gets comprehensive user context from the database."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get user profile
    cursor.execute("""
        SELECT name, current_main_focus, daily_study_goal_minutes, archetype
        FROM User_Profile WHERE user_id = 1
    """)
    profile = cursor.fetchone()

    if not profile:
        conn.close()
        return "No user profile found."

    name, main_focus, daily_goal, archetype = profile

    # Get current goals
    cursor.execute("""
        SELECT description, target_date, status
        FROM Goals
        WHERE user_id = 1 AND status = 'Active'
        ORDER BY priority_level DESC
        LIMIT 3
    """)
    goals = cursor.fetchall()

    # Get recent practice sessions
    cursor.execute("""
        SELECT pl.log_date, s.concept_id, pl.duration_minutes,
               pl.user_notes_difficulty, pl.confidence_score
        FROM Practice_Log pl
        JOIN Skills s ON pl.skill_id = s.skill_id
        WHERE pl.user_id = 1
        ORDER BY pl.log_date DESC
        LIMIT 3
    """)
    recent_practice = cursor.fetchall()

    # Get current skill levels
    cursor.execute("""
        SELECT concept_id, competence_level, confidence_score, practice_count
        FROM Skills
        WHERE user_id = 1 AND competence_level != 'Unknown'
        ORDER BY last_updated DESC
        LIMIT 5
    """)
    skills = cursor.fetchall()

    # Get active distractions
    cursor.execute("""
        SELECT description, trigger_description, impact_level
        FROM Distraction_Habits
        WHERE user_id = 1 AND status = 'Identified'
        ORDER BY impact_level DESC
        LIMIT 3
    """)
    distractions = cursor.fetchall()

    # Get upcoming scheduled events
    cursor.execute("""
        SELECT event_description, scheduled_start_time, event_type
        FROM Scheduled_Events
        WHERE user_id = 1 AND scheduled_start_time > datetime('now')
        ORDER BY scheduled_start_time
        LIMIT 3
    """)
    upcoming_events = cursor.fetchall()

    conn.close()

    # Build comprehensive context string
    context = f"User: {name} (Archetype: {archetype})\n"
    context += f"Current Main Focus: {main_focus}\n"
    context += f"Daily Study Goal: {daily_goal} minutes\n"

    if goals:
        context += "\nActive Goals:\n"
        for desc, target, status in goals:
            context += f"- {desc} (Target: {target or 'No deadline'}, Status: {status})\n"

    if skills:
        context += "\nCurrent Skills:\n"
        for concept_id, level, confidence, practice_count in skills:
            context += f"- {concept_id}: {level} (Confidence: {confidence:.1f}, Practiced: {practice_count}x)\n"

    if recent_practice:
        context += "\nRecent Practice Sessions:\n"
        for date, concept_id, duration, notes, confidence in recent_practice:
            context += f"- {date}: {concept_id} for {duration} minutes"
            if confidence:
                context += f" (Confidence: {confidence}/5)"
            if notes:
                context += f" (Notes: {notes})"
            context += "\n"

    if distractions:
        context += "\nActive Challenges:\n"
        for desc, trigger, impact in distractions:
            context += f"- {desc} (Trigger: {trigger or 'Unknown'}, Impact: {impact}/5)\n"

    if upcoming_events:
        context += "\nUpcoming Schedule:\n"
        for desc, start_time, event_type in upcoming_events:
            context += f"- {start_time}: {desc} ({event_type})\n"

    return context


def manage_coach_response(user_input, collection, session_id=None):
    """
    Orchestrates the coach's response with intelligent context awareness.
    """
    # 1. Get intelligent context (includes patterns and history)
    user_context = get_intelligent_context()

    # 2. Get current time context
    now = datetime.datetime.now()
    time_context = f"Current time: {now.strftime('%A, %B %d, %Y at %I:%M %p')}"

    # 3. Find relevant knowledge from ChromaDB
    knowledge_context = query_knowledge_base(collection, user_input)
    knowledge_prompt_section = "\n\n--- Relevant Knowledge ---\n" + \
        "\n".join(knowledge_context) if knowledge_context else ""

    # 4. Check for pattern-based interventions
    pattern_intervention = get_pattern_intervention(user_input, collection)
    if pattern_intervention:
        knowledge_prompt_section += f"\n\n--- Pattern-Based Guidance ---\n{pattern_intervention}"

    # 5. Build a comprehensive prompt for Gemini
    prompt = f"""
    You are a world-class personal learning coach based on the 'iCanStudy' methodology.
    Your tone is encouraging, precise, and proactive. You provide clear, actionable guidance and ask insightful questions.

    IMPORTANT INSTRUCTIONS:
    - Keep responses concise (2-3 sentences max)
    - Always end with a question to keep the conversation going
    - Use the user's name when appropriate
    - Reference their current progress and context
    - If they ask about time, date, or personal info, use the context provided
    - Focus on their current main focus area but integrate other phases when helpful
    - If patterns are identified, address them with specific knowledge-based solutions
    - Be proactive in guiding the user toward better learning habits

    {time_context}

    USER CONTEXT:
    {user_context}

    {knowledge_prompt_section}

    User's Input: "{user_input}"

    Your Response:
    """

    # 6. Get and return the response
    coach_response = get_gemini_response(prompt)

    # 7. Log this interaction with pattern analysis
    log_interaction(user_input, coach_response, session_id)

    return coach_response


def get_pattern_intervention(user_input, collection):
    """Gets specific interventions based on detected patterns."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Check for active patterns that might be relevant
    cursor.execute("""
        SELECT pattern_description, suggested_interventions
        FROM Pattern_Analysis
        WHERE user_id = 1 AND status = 'active' AND pattern_strength > 0.6
    """)
    patterns = cursor.fetchall()

    conn.close()

    user_input_lower = user_input.lower()
    interventions = []

    for description, suggested_interventions in patterns:
        # Check if user input relates to this pattern
        pattern_keywords = description.lower().split()
        if any(keyword in user_input_lower for keyword in pattern_keywords):
            if suggested_interventions:
                # Query knowledge base for specific interventions
                intervention_concepts = query_knowledge_base(
                    collection, description, n_results=2)
                if intervention_concepts:
                    interventions.extend(intervention_concepts)

    return "\n".join(interventions) if interventions else None


def start_session():
    """Starts a new coaching session and returns session_id."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    current_date = datetime.datetime.now().date()
    current_time = datetime.datetime.now()

    # Check if there's already a session today
    cursor.execute("""
        SELECT session_id FROM Session_Logs
        WHERE user_id = 1 AND session_date = ?
    """, (current_date,))

    existing_session = cursor.fetchone()

    if existing_session:
        session_id = existing_session[0]
        # Update session start time if resuming
        cursor.execute("""
            UPDATE Session_Logs
            SET session_start_time = ?
            WHERE session_id = ?
        """, (current_time, session_id))
    else:
        # Create new session
        cursor.execute("""
            INSERT INTO Session_Logs (user_id, session_date, session_start_time)
            VALUES (?, ?, ?)
        """, (1, current_date, current_time))
        session_id = cursor.lastrowid

    conn.commit()
    conn.close()
    return session_id


def end_session(session_id):
    """Ends the current session and analyzes patterns."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    current_time = datetime.datetime.now()

    # Update session end time
    cursor.execute("""
        UPDATE Session_Logs
        SET session_end_time = ?
        WHERE session_id = ?
    """, (current_time, session_id))

    # Analyze patterns from today's session
    analyze_session_patterns(session_id)

    conn.commit()
    conn.close()


def log_interaction(user_input, coach_response, session_id=None):
    """Logs the interaction with pattern analysis."""
    if not session_id:
        return

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # Log to Coach_Interactions
        cursor.execute("""
            INSERT INTO Coach_Interactions
            (user_id, interaction_type, coach_message, user_response)
            VALUES (?, ?, ?, ?)
        """, (1, 'reactive', coach_response, user_input))

        # Update session interaction count
        cursor.execute("""
            UPDATE Session_Logs
            SET total_interactions = total_interactions + 1
            WHERE session_id = ?
        """, (session_id,))

        # Check for distraction patterns in the same connection
        check_distraction_patterns_with_conn(user_input, conn)

        conn.commit()
    finally:
        conn.close()


def check_distraction_patterns_with_conn(user_input, conn):
    """Analyzes user input for distraction patterns using existing connection."""
    cursor = conn.cursor()

    # Common distraction keywords
    distraction_keywords = ['chess', 'social media', 'phone', 'youtube', 'netflix',
                            'instagram', 'twitter', 'facebook', 'games', 'tv']

    user_input_lower = user_input.lower()

    for keyword in distraction_keywords:
        if keyword in user_input_lower:
            # Check if this pattern already exists
            cursor.execute("""
                SELECT pattern_id, pattern_strength FROM Pattern_Analysis
                WHERE user_id = 1 AND pattern_type = 'distraction'
                AND pattern_description LIKE ?
            """, (f'%{keyword}%',))

            existing_pattern = cursor.fetchone()

            if existing_pattern:
                # Update existing pattern
                pattern_id, current_strength = existing_pattern
                new_strength = min(1.0, current_strength + 0.1)
                cursor.execute("""
                    UPDATE Pattern_Analysis
                    SET pattern_strength = ?, last_observed = ?
                    WHERE pattern_id = ?
                """, (new_strength, datetime.datetime.now().date(), pattern_id))
            else:
                # Create new pattern
                cursor.execute("""
                    INSERT INTO Pattern_Analysis
                    (user_id, pattern_type, pattern_description, frequency,
                     pattern_strength, first_detected, last_observed)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (1, 'distraction', f'{keyword.title()} distraction pattern',
                      'situational', 0.3, datetime.datetime.now().date(),
                      datetime.datetime.now().date()))


def check_distraction_patterns(user_input):
    """Analyzes user input for distraction patterns."""
    conn = sqlite3.connect(DB_PATH)
    try:
        check_distraction_patterns_with_conn(user_input, conn)
        conn.commit()
    finally:
        conn.close()


def analyze_session_patterns(session_id):
    """Analyzes patterns from the current session."""
    # This would contain more sophisticated pattern analysis
    # For now, we'll keep it simple
    pass


def get_intelligent_context():
    """Gets smart context based on assessment insights, patterns, and recent history."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get basic user context
    basic_context = get_user_context()

    # Get latest assessment insights (most important for coaching)
    cursor.execute("""
        SELECT assessment_type, main_weaknesses, main_strengths, recommended_focus_areas, created_at
        FROM User_Assessment
        WHERE user_id = 1
        ORDER BY created_at DESC
        LIMIT 1
    """)
    assessment = cursor.fetchone()

    # Get recent patterns (last 30 days)
    cursor.execute("""
        SELECT pattern_type, pattern_description, pattern_strength,
               suggested_interventions, status
        FROM Pattern_Analysis
        WHERE user_id = 1 AND last_observed >= date('now', '-30 days')
        AND pattern_strength > 0.5
        ORDER BY pattern_strength DESC
        LIMIT 3
    """)
    patterns = cursor.fetchall()

    # Get yesterday's session summary
    cursor.execute("""
        SELECT main_topics_discussed, problems_identified, effectiveness_score
        FROM Session_Logs
        WHERE user_id = 1 AND session_date = date('now', '-1 day')
    """)
    yesterday = cursor.fetchone()

    conn.close()

    # Build intelligent context
    context = basic_context

    # Add assessment insights (critical for personalized coaching)
    if assessment:
        assessment_type, weaknesses_json, strengths_json, focus_areas_json, created_at = assessment
        try:
            weaknesses = json.loads(weaknesses_json) if weaknesses_json else []
            strengths = json.loads(strengths_json) if strengths_json else []
            focus_areas = json.loads(
                focus_areas_json) if focus_areas_json else []

            context += f"\n\nAssessment Insights ({assessment_type}):\n"
            if strengths and strengths != ["To be determined through observation"]:
                context += f"- Strengths: {', '.join(strengths)}\n"
            if weaknesses:
                context += f"- Key Weaknesses: {', '.join(weaknesses)}\n"
            if focus_areas:
                context += f"- Priority Focus Areas: {', '.join(focus_areas)}\n"
        except json.JSONDecodeError:
            context += f"\n\nAssessment Available ({assessment_type}) - Raw data needs parsing\n"

    if patterns:
        context += "\n\nIdentified Patterns:\n"
        for pattern_type, description, strength, interventions, status in patterns:
            context += f"- {description} (Strength: {strength:.1f}, Status: {status})\n"

    if yesterday:
        topics, problems, effectiveness = yesterday
        if topics or problems:
            context += f"\nYesterday's Session:\n"
            if topics:
                context += f"- Topics: {topics}\n"
            if problems:
                context += f"- Challenges: {problems}\n"
            if effectiveness:
                context += f"- Effectiveness: {effectiveness:.1f}/5\n"

    return context


def get_assessment_based_interventions(user_input, collection):
    """Gets specific coaching interventions based on assessment insights."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get latest assessment data
    cursor.execute("""
        SELECT main_weaknesses, main_strengths, recommended_focus_areas
        FROM User_Assessment
        WHERE user_id = 1
        ORDER BY created_at DESC
        LIMIT 1
    """)
    assessment = cursor.fetchone()

    conn.close()

    if not assessment:
        return None

    weaknesses_json, strengths_json, focus_areas_json = assessment

    try:
        weaknesses = json.loads(weaknesses_json) if weaknesses_json else []
        focus_areas = json.loads(focus_areas_json) if focus_areas_json else []

        user_input_lower = user_input.lower()
        interventions = []

        # Check if user input relates to identified weaknesses
        for weakness in weaknesses:
            if any(keyword in user_input_lower for keyword in weakness.lower().split()):
                # Get specific interventions from knowledge base
                if "urgency trap" in weakness.lower():
                    intervention_concepts = query_knowledge_base(
                        collection, "Eisenhower Matrix urgent important", n_results=1)
                elif "passive re-reading" in weakness.lower():
                    intervention_concepts = query_knowledge_base(
                        collection, "active recall retrieval practice", n_results=1)
                elif "fixed mindset" in weakness.lower():
                    intervention_concepts = query_knowledge_base(
                        collection, "growth mindset failure learning", n_results=1)
                elif "no calendar" in weakness.lower():
                    intervention_concepts = query_knowledge_base(
                        collection, "schedule planning time management", n_results=1)
                elif "distracting environment" in weakness.lower():
                    intervention_concepts = query_knowledge_base(
                        collection, "environment focus distraction", n_results=1)
                else:
                    intervention_concepts = query_knowledge_base(
                        collection, weakness, n_results=1)

                if intervention_concepts:
                    interventions.extend(intervention_concepts)

        # Check if user input relates to focus areas
        for focus_area in focus_areas:
            if any(keyword in user_input_lower for keyword in focus_area.lower().split()):
                focus_concepts = query_knowledge_base(
                    collection, focus_area, n_results=1)
                if focus_concepts:
                    interventions.extend(focus_concepts)

        return "\n".join(interventions) if interventions else None

    except json.JSONDecodeError:
        return None


def add_or_update_skill(skill_name, phase, level):
    """Adds or updates a skill in the database."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    current_time = datetime.datetime.now().isoformat()

    cursor.execute("""
        INSERT OR REPLACE INTO Skills (user_id, concept_id, competence_level, last_updated)
        VALUES (?, ?, ?, ?)
    """, (1, skill_name, level, current_time))

    conn.commit()
    conn.close()
    print(f"Updated skill: {skill_name} ({phase}) -> {level}")


def log_practice_block(skill_name, duration_minutes, difficulty_notes=""):
    """Logs a practice session."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get skill ID using new schema
    cursor.execute(
        "SELECT skill_id FROM Skills WHERE concept_id = ?", (skill_name,))
    skill_result = cursor.fetchone()

    if not skill_result:
        print(f"Skill '{skill_name}' not found. Adding it first...")
        add_or_update_skill(skill_name, "Enablers", "developing")
        cursor.execute(
            "SELECT skill_id FROM Skills WHERE concept_id = ?", (skill_name,))
        skill_result = cursor.fetchone()

    skill_id = skill_result[0]
    current_date = datetime.datetime.now().isoformat()

    cursor.execute("""
        INSERT INTO Practice_Log (user_id, skill_id, duration_minutes, user_notes_difficulty)
        VALUES (?, ?, ?, ?)
    """, (1, skill_id, duration_minutes, difficulty_notes))

    conn.commit()
    conn.close()
    print(
        f"Logged practice session: {skill_name} for {duration_minutes} minutes")


def add_distraction_habit(habit_type, description):
    """Adds a new distraction or habit to track."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("""
        INSERT INTO Distraction_Habits (user_id, description, status)
        VALUES (?, ?, 'Identified')
    """, (1, description))

    conn.commit()
    conn.close()
    print(f"Added {habit_type}: {description}")


def update_main_focus(new_focus):
    """Updates the user's main focus area."""
    if new_focus not in ['Enablers', 'Retrieval', 'Encoding']:
        print("Invalid focus area. Must be: Enablers, Retrieval, or Encoding")
        return

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("""
        UPDATE User_Profile SET current_main_focus = ? WHERE user_id = 1
    """, (new_focus,))

    conn.commit()
    conn.close()
    print(f"Updated main focus to: {new_focus}")


def handle_special_commands(user_input, collection):
    """Handles special commands (starting with /) for data management."""
    user_input_lower = user_input.lower().strip()

    # Only process commands that start with "/"
    if not user_input_lower.startswith("/"):
        return None

    # Practice block logging
    if user_input_lower.startswith("/log practice"):
        try:
            # Expected format: "/log practice [skill] [duration] [notes]"
            parts = user_input.split(" ", 3)
            if len(parts) >= 3:
                skill = parts[2]
                duration = int(parts[3]) if len(
                    parts) > 3 and parts[3].isdigit() else 30
                notes = parts[4] if len(parts) > 4 else ""
                log_practice_block(skill, duration, notes)
                return f"✅ Practice session logged! How did that feel?"
        except (ValueError, IndexError):
            return "Format: '/log practice [skill_name] [duration_minutes] [optional_notes]'"

    # Skill level updates
    elif user_input_lower.startswith("/update skill"):
        try:
            # Expected format: "/update skill [skill] [phase] [level]"
            parts = user_input.split(" ", 4)
            if len(parts) >= 5:
                skill = parts[2]
                phase = parts[3]
                level = parts[4]
                add_or_update_skill(skill, phase, level)
                return f"✅ Skill updated! What would you like to work on next?"
        except IndexError:
            return "Format: '/update skill [skill_name] [phase] [level]'"

    # Focus area changes
    elif user_input_lower.startswith("/change focus"):
        try:
            parts = user_input.split(" ", 2)
            if len(parts) >= 3:
                new_focus = parts[2].title()
                update_main_focus(new_focus)
                return f"✅ Focus changed to {new_focus}! Ready to dive deeper into this area?"
        except IndexError:
            return "Format: '/change focus [Enablers/Retrieval/Encoding]'"

    # Add distractions
    elif user_input_lower.startswith("/add distraction"):
        try:
            description = user_input[16:].strip()  # Remove "/add distraction "
            add_distraction_habit("distraction", description)
            return f"✅ Distraction noted. What strategies have worked for you before with similar challenges?"
        except:
            return "Format: '/add distraction [description]'"

    # Assessment command
    elif user_input_lower.startswith("/assess"):
        conduct_initial_assessment()
        return "✅ Assessment completed! I now understand you much better."

    # Show current status
    elif user_input_lower.startswith("/status"):
        return get_user_status_summary()

    return f"❌ Unknown command. Type '/help' for available commands."


def parse_conversation_for_activities(user_input):
    """Automatically detects and logs activities mentioned in conversation."""
    user_input_lower = user_input.lower()
    results = []

    # Common patterns for practice activities
    practice_patterns = [
        (r"practiced (.+?) for (\d+) minutes?", "practice"),
        (r"studied (.+?) for (\d+) minutes?", "practice"),
        (r"worked on (.+?) for (\d+) minutes?", "practice"),
        (r"spent (\d+) minutes? on (.+)", "practice_reverse"),
        (r"did (\d+) minutes? of (.+)", "practice_reverse")
    ]

    import re
    for pattern, activity_type in practice_patterns:
        match = re.search(pattern, user_input_lower)
        if match:
            if activity_type == "practice_reverse":
                duration = int(match.group(1))
                skill = match.group(2).strip()
            else:
                skill = match.group(1).strip()
                duration = int(match.group(2))

            # Auto-log the practice session
            try:
                log_practice_block(
                    skill, duration, f"Auto-detected from conversation")
                results.append(
                    f"📝 I noticed you practiced {skill} for {duration} minutes - logged automatically!")
            except:
                pass

    # Detect learning challenges and struggles
    challenge_detected = detect_learning_challenges(user_input)
    if challenge_detected:
        results.append(challenge_detected)

    return "\n".join(results) if results else None


def detect_learning_challenges(user_input):
    """Automatically detects and stores learning challenges mentioned in conversation."""
    user_input_lower = user_input.lower()
    import re

    # Patterns for detecting struggles with specific subjects/topics
    struggle_patterns = [
        (r"struggling with (.+)", "struggle"),
        (r"having trouble with (.+)", "struggle"),
        (r"difficult (.+)", "difficulty"),
        (r"hard to understand (.+)", "difficulty"),
        (r"confused about (.+)", "confusion"),
        (r"don't understand (.+)", "confusion"),
        (r"how to study (.+)", "study_help"),
        (r"how to learn (.+)", "study_help"),
        (r"help with (.+)", "help_request"),
        (r"stuck on (.+)", "stuck"),
        (r"can't figure out (.+)", "stuck")
    ]

    for pattern, challenge_type in struggle_patterns:
        match = re.search(pattern, user_input_lower)
        if match:
            topic = match.group(1).strip()

            # Clean up the topic (remove common words)
            topic = re.sub(
                r'\b(the|a|an|my|this|that|these|those)\b', '', topic).strip()

            if topic and len(topic) > 2:  # Only store meaningful topics
                try:
                    # Store the challenge in the database
                    store_learning_challenge(topic, challenge_type, user_input)
                    return f"🎯 I've noted that you're working on {topic} - I'll help you develop strategies for this!"
                except:
                    pass

    return None


def store_learning_challenge(topic, challenge_type, original_input):
    """Stores a detected learning challenge in the database."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Check if this challenge already exists
    cursor.execute("""
        SELECT habit_id FROM Distraction_Habits
        WHERE user_id = 1 AND description LIKE ? AND status = 'Identified'
    """, (f"%{topic}%",))

    existing = cursor.fetchone()

    if not existing:
        # Create new challenge entry
        description = f"Learning challenge: {topic}"
        trigger_description = f"Detected from: '{original_input}'"

        cursor.execute("""
            INSERT INTO Distraction_Habits
            (user_id, description, trigger_description, status, frequency, impact_level)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (1, description, trigger_description, 'Identified', 'Unknown', 3))

        conn.commit()

    conn.close()


def get_user_status_summary():
    """Provides a comprehensive status summary."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get basic info
    cursor.execute(
        "SELECT name, current_main_focus FROM User_Profile WHERE user_id = 1")
    profile = cursor.fetchone()

    # Get current module
    cursor.execute(
        "SELECT current_module, completion_percentage FROM Course_Progress WHERE user_id = 1")
    progress = cursor.fetchone()

    # Get recent practice
    cursor.execute("""
        SELECT COUNT(*) FROM Practice_Log
        WHERE user_id = 1 AND log_date >= date('now', '-7 days')
    """)
    recent_practice = cursor.fetchone()[0]

    # Get active challenges
    cursor.execute("""
        SELECT COUNT(*) FROM Distraction_Habits
        WHERE user_id = 1 AND status = 'Identified'
    """)
    active_challenges = cursor.fetchone()[0]

    conn.close()

    if profile:
        name, focus = profile
        current_module = progress[0] if progress else "module_01"
        completion = progress[1] if progress else 0.0

        status = f"""
📊 STATUS SUMMARY for {name}
{'='*40}
🎯 Current Focus: {focus}
📚 Course Module: {current_module} ({completion:.1f}% complete)
💪 Practice Sessions (last 7 days): {recent_practice}
⚠️  Active Challenges: {active_challenges}
{'='*40}
        """
        return status.strip()

    return "❌ No user profile found."


def check_proactive_triggers():
    """
    Checks for conditions to start a conversation proactively.
    """
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get user name for personalization
    cursor.execute("SELECT name FROM User_Profile WHERE user_id = 1")
    name_result = cursor.fetchone()
    name = name_result[0] if name_result else "there"

    now = datetime.datetime.now()
    current_hour = now.hour
    current_weekday = now.weekday()  # 0 = Monday, 6 = Sunday

    # Morning greeting (6 AM - 12 PM)
    if 6 <= current_hour < 12:
        return f"Good morning, {name}! Ready to tackle your learning goals today? What's your main focus?"

    # Afternoon check-in (12 PM - 6 PM)
    elif 12 <= current_hour < 18:
        return f"Good afternoon, {name}! How's your learning progress going today?"

    # Evening reflection (6 PM - 10 PM)
    elif 18 <= current_hour < 22:
        return f"Good evening, {name}! Time to reflect on today's learning. What went well?"

    # Check for practice reminders
    cursor.execute("""
        SELECT MAX(log_date) FROM Practice_Log WHERE user_id = 1
    """)
    last_practice = cursor.fetchone()[0]

    if last_practice:
        last_practice_date = datetime.datetime.fromisoformat(last_practice)
        days_since_practice = (now - last_practice_date).days

        if days_since_practice >= 2:
            conn.close()
            return f"Hi {name}! I noticed it's been {days_since_practice} days since your last practice session. Ready to get back into it?"

    # Weekly reflection trigger (Friday or Saturday)
    if current_weekday in [4, 5]:  # Friday or Saturday
        conn.close()
        return f"Hey {name}! It's the weekend - perfect time for a weekly reflection. What were your biggest learning wins this week?"

    conn.close()
    return None


def main_loop(collection):
    """The main interactive loop for the coach with intelligent session management."""
    # Start session tracking
    session_id = start_session()

    print("🎯 Welcome to your Personalized AI Coach!")
    print("💬 Just talk to me naturally - I'll understand and help you learn effectively.")
    print("⚡ Commands start with '/' - Type '/help' for available commands")
    print("🚪 Type 'quit' to exit")

    # Intelligent proactive coaching based on time and user state
    initial_prompt = get_intelligent_coaching_prompt(collection)
    if initial_prompt:
        print(f"\n🤖 Coach: {initial_prompt}")

    while True:
        try:
            user_input = input("\n👤 You: ")
            if user_input.lower() in ['quit', 'exit', 'bye']:
                print(
                    "🤖 Coach: Great session! Keep building those learning habits. See you next time!")
                end_session(session_id)
                break

            if user_input.lower() in ['help', '/help']:
                show_help_menu()
                continue

            if user_input:
                # Check for commands first (starting with /)
                special_response = handle_special_commands(
                    user_input, collection)
                if special_response:
                    print(f"\n🤖 Coach: {special_response}")
                    log_interaction(user_input, special_response, session_id)
                else:
                    # Check for auto-detectable activities in conversation
                    auto_log = parse_conversation_for_activities(user_input)
                    if auto_log:
                        print(f"\n📝 {auto_log}")

                    # Regular conversation with intelligent coaching
                    coach_response = manage_intelligent_coaching_response(
                        user_input, collection, session_id)
                    print(f"\n🤖 Coach: {coach_response}")

        except (KeyboardInterrupt, EOFError):
            print("\n🤖 Coach: Session ended. Keep up the great work!")
            end_session(session_id)
            break


def show_help_menu():
    """Shows comprehensive help menu."""
    print("""
🆘 HELP MENU
{'='*50}
💬 CONVERSATION:
   Just talk naturally! I understand:
   - "I practiced math for 30 minutes"
   - "I'm struggling with procrastination"
   - "What's the best way to study?"

⚡ COMMANDS (start with /):
   /log practice [skill] [minutes] [notes]
   /update skill [skill] [phase] [level]
   /change focus [Enablers/Retrieval/Encoding]
   /add distraction [description]
   /status - Show your current progress
   /assess - Take comprehensive assessment

📚 COURSE GUIDANCE:
   I'll guide you through the iCanStudy methodology
   based on your current module and progress.

🎯 SMART FEATURES:
   ✅ Auto-detects practice from conversation
   ✅ Recognizes patterns and suggests solutions
   ✅ Provides time-aware coaching
   ✅ Tracks your course progress
{'='*50}
""")


def get_intelligent_coaching_prompt(collection):
    """Generates intelligent coaching prompt based on current context."""
    now = datetime.datetime.now()
    current_hour = now.hour

    # Critical time-based interventions
    if current_hour >= 23 or current_hour <= 5:
        return f"⚠️ Jorge, it's {now.strftime('%I:%M %p')} - you should be sleeping! Good sleep is fundamental for learning and memory consolidation. What's keeping you up? Let's address this so you can get proper rest."

    # Get user context for intelligent prompting
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Check if user has completed assessment
    cursor.execute("SELECT COUNT(*) FROM User_Assessment WHERE user_id = 1")
    has_assessment = cursor.fetchone()[0] > 0

    if not has_assessment:
        conn.close()
        return "I need to understand you better to provide effective coaching. Let's start with a comprehensive assessment. Type '/assess' when you're ready (takes 5-10 minutes)."

    # Check for critical missing foundations
    cursor.execute("""
        SELECT description FROM Distraction_Habits
        WHERE user_id = 1 AND impact_level = 5 AND status = 'Identified'
    """)
    critical_issues = cursor.fetchall()

    if critical_issues:
        issue = critical_issues[0][0]
        conn.close()
        return f"🚨 CRITICAL: {issue}. This is blocking your learning progress. Let's fix this immediately - everything else depends on having solid foundations."

    # Regular time-based coaching
    conn.close()

    if 6 <= current_hour < 12:
        return f"Good morning, Jorge! Ready to tackle your learning goals? What's your main focus for today?"
    elif 12 <= current_hour < 18:
        return f"Good afternoon! How's your learning progress going today?"
    else:
        return f"Good evening! Time to reflect on today's learning. What went well?"


def load_strategic_brain():
    """Loads the strategic brain (coach_brain.json) for coaching intelligence."""
    brain_path = os.path.join(os.path.dirname(
        __file__), 'Coach_Brain', 'coach_brain.json')
    try:
        with open(brain_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Warning: Strategic brain not found at {brain_path}")
        return []


def smart_knowledge_search(user_input):
    """Improved knowledge search that finds specific concepts accurately."""
    user_input_lower = user_input.lower()

    # Direct concept name matching
    concept_matches = []

    # Search through all JSON files for exact matches
    for filename in os.listdir(KNOWLEDGE_BASE_DIR):
        if filename.endswith('.json'):
            filepath = os.path.join(KNOWLEDGE_BASE_DIR, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for item in data:
                        # Check concept name
                        concept_name = item.get('concept_name', '').lower()
                        if any(term in concept_name for term in ['traffic light', 'tls']):
                            if any(term in user_input_lower for term in ['traffic light', 'tls']):
                                concept_matches.append(
                                    format_concept_for_display(item, filename))

                        # Check keywords
                        keywords = item.get('keywords', [])
                        if isinstance(keywords, list):
                            for keyword in keywords:
                                if keyword.lower() in user_input_lower:
                                    concept_matches.append(
                                        format_concept_for_display(item, filename))
                                    break

                        # Check for other specific matches
                        if any(term in user_input_lower for term in ['feynman', 'zeigarnik', 'pareto', 'mvg', 'minimum viable']):
                            if any(term in concept_name for term in ['feynman', 'zeigarnik', 'pareto', 'mvg', 'minimum viable']):
                                concept_matches.append(
                                    format_concept_for_display(item, filename))

            except Exception as e:
                continue

    # Remove duplicates and return top matches
    unique_matches = list(dict.fromkeys(concept_matches))
    return "\n\n".join(unique_matches[:3]) if unique_matches else "No specific matches found."


def format_concept_for_display(concept, filename):
    """Formats a concept for display in the knowledge context."""
    return f"""**{concept.get('concept_name', 'Unknown')}** (from {filename})
Phase: {concept.get('ics_phase', 'Unknown')}
Summary: {concept.get('summary', 'No summary available')}
Instructions: {' '.join(concept.get('instructions', []))}
Context: {concept.get('additional_context', 'No additional context')}"""


def format_strategic_brain_context(strategic_brain, user_input, user_context):
    """Formats strategic brain guidance for the AI prompt."""
    if not strategic_brain:
        return "Strategic brain not available."

    user_input_lower = user_input.lower()
    relevant_guidance = []

    # Find relevant scenario handlers
    for item in strategic_brain:
        if item.get('concept_type') == 'ScenarioHandler':
            trigger_keywords = item.get('trigger_keywords', [])
            if any(keyword in user_input_lower for keyword in trigger_keywords):
                relevant_guidance.append(f"""
**SCENARIO DETECTED: {item['concept_name']}**
Diagnosis: {item['user_state_diagnosis']}
Objective: {item['coach_objective']}
Playbook: {' '.join(item['playbook'])}
""")

    # Add core principles
    principles = [item for item in strategic_brain if item.get(
        'concept_type') == 'CorePrinciple']
    if principles:
        relevant_guidance.append("\n**CORE PRINCIPLES TO FOLLOW:**")
        for principle in principles[:2]:  # Top 2 principles
            relevant_guidance.append(
                f"- {principle['concept_name']}: {principle['summary']}")

    return "\n".join(relevant_guidance) if relevant_guidance else "Apply general coaching principles."


def manage_intelligent_coaching_response(user_input, collection, session_id):
    """AGENTIC coaching response using the two-brain system."""

    # Get comprehensive context about the user
    user_context = get_intelligent_context()

    # Get current time context
    now = datetime.datetime.now()
    time_context = f"Current time: {now.strftime('%A, %B %d, %Y at %I:%M %p')}"

    # Load the Strategic Brain (coach_brain.json)
    strategic_brain = load_strategic_brain()

    # Get relevant knowledge using improved search
    relevant_knowledge = smart_knowledge_search(user_input)

    # Get any stored patterns or challenges related to this conversation
    stored_patterns = get_relevant_stored_patterns(user_input)

    # Build an intelligent prompt using the two-brain system
    strategic_guidance = format_strategic_brain_context(
        strategic_brain, user_input, user_context)

    prompt = f"""
You are an intelligent AI Learning Coach based on the iCanStudy methodology. You have access to two specialized "brains":

1. **STRATEGIC BRAIN** - Your coaching intelligence, psychology, and pedagogy
2. **KNOWLEDGE BRAIN** - The factual content and techniques from the iCanStudy course

STRATEGIC BRAIN GUIDANCE:
{strategic_guidance}

CURRENT SITUATION:
{time_context}
User says: "{user_input}"

USER CONTEXT & HISTORY:
{user_context}

RELEVANT KNOWLEDGE FROM YOUR KNOWLEDGE BRAIN:
{relevant_knowledge}

STORED PATTERNS & CHALLENGES:
{stored_patterns}

YOUR TASK:
Use your Strategic Brain to analyze this situation and decide how to coach this user. Then use your Knowledge Brain to provide specific, accurate information about techniques and concepts.

COACHING APPROACH:
1. Analyze the user's archetype and current state
2. Identify which scenario handler or strategy applies
3. Use the appropriate coaching principles and directives
4. Provide specific knowledge from your Knowledge Brain
5. Give actionable, personalized guidance

Think through this situation intelligently and respond as an expert coach would.

Your response:
"""

    # Let the AI think and respond intelligently
    coach_response = get_gemini_response(prompt)

    # Store any new information discovered in this conversation
    store_conversation_insights(user_input, coach_response, session_id)

    # Log the interaction
    log_interaction(user_input, coach_response, session_id)

    return coach_response


def get_relevant_stored_patterns(user_input):
    """Gets relevant stored patterns and challenges from the database."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get stored challenges and patterns
    cursor.execute("""
        SELECT description, trigger_description, status, impact_level
        FROM Distraction_Habits
        WHERE user_id = 1
        ORDER BY impact_level DESC, created_at DESC
        LIMIT 5
    """)
    challenges = cursor.fetchall()

    # Get recent patterns
    cursor.execute("""
        SELECT pattern_description, pattern_strength, status
        FROM Pattern_Analysis
        WHERE user_id = 1 AND status = 'active'
        ORDER BY pattern_strength DESC
        LIMIT 3
    """)
    patterns = cursor.fetchall()

    conn.close()

    result = []
    if challenges:
        result.append("STORED LEARNING CHALLENGES:")
        for desc, trigger, status, impact in challenges:
            result.append(f"- {desc} (Status: {status}, Impact: {impact}/5)")

    if patterns:
        result.append("\nIDENTIFIED PATTERNS:")
        for desc, strength, status in patterns:
            result.append(
                f"- {desc} (Confidence: {strength:.1f}, Status: {status})")

    return "\n".join(result) if result else "No specific patterns or challenges stored yet."


def store_conversation_insights(user_input, coach_response, session_id):
    """Intelligently stores insights discovered from the conversation."""
    # Auto-detect and store learning challenges
    challenge_detected = detect_learning_challenges(user_input)

    # Store the conversation context for pattern analysis
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Update session logs with topics discussed
    topics = extract_topics_from_conversation(user_input, coach_response)
    if topics:
        cursor.execute("""
            UPDATE Session_Logs
            SET main_topics_discussed = ?, total_interactions = total_interactions + 1
            WHERE session_id = ?
        """, (topics, session_id))
        conn.commit()

    conn.close()


def extract_topics_from_conversation(user_input, coach_response):
    """Extracts main topics from the conversation for storage."""
    import re

    # Common academic subjects and topics
    subjects = ['math', 'mathematics', 'calculus', 'algebra', 'geometry', 'statistics',
                'physics', 'chemistry', 'biology', 'history', 'english', 'literature',
                'programming', 'computer science', 'economics', 'psychology']

    topics_found = []
    user_lower = user_input.lower()

    for subject in subjects:
        if subject in user_lower:
            topics_found.append(subject)

    # Look for specific topics mentioned
    topic_patterns = [
        r'linear regression', r'machine learning', r'calculus', r'derivatives',
        r'integrals', r'probability', r'statistics', r'algorithms'
    ]

    for pattern in topic_patterns:
        if re.search(pattern, user_lower):
            topics_found.append(pattern.replace(r'\\', ''))

    return ', '.join(set(topics_found)) if topics_found else None


# Removed hardcoded interventions - the AI agent now decides intelligently


def check_proactive_triggers_with_patterns(collection):
    """Enhanced proactive triggers that consider patterns and history."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get user name for personalization
    cursor.execute("SELECT name FROM User_Profile WHERE user_id = 1")
    name_result = cursor.fetchone()
    name = name_result[0] if name_result else "there"

    now = datetime.datetime.now()
    current_hour = now.hour

    # Check for strong patterns that need immediate attention
    cursor.execute("""
        SELECT pattern_description, pattern_strength
        FROM Pattern_Analysis
        WHERE user_id = 1 AND status = 'active' AND pattern_strength > 0.8
        ORDER BY pattern_strength DESC
        LIMIT 1
    """)
    strong_pattern = cursor.fetchone()

    # Check yesterday's effectiveness
    cursor.execute("""
        SELECT effectiveness_score, problems_identified
        FROM Session_Logs
        WHERE user_id = 1 AND session_date = date('now', '-1 day')
    """)
    yesterday = cursor.fetchone()

    conn.close()

    # Priority 1: Address strong patterns
    if strong_pattern:
        description, strength = strong_pattern
        # Get intervention from knowledge base
        interventions = query_knowledge_base(
            collection, description, n_results=1)
        if interventions:
            return f"Hi {name}! I've noticed a strong pattern: {description}. Let's tackle this together. {interventions[0][:200]}... Ready to work on this?"

    # Priority 2: Follow up on yesterday's challenges
    if yesterday:
        effectiveness, problems = yesterday
        if effectiveness and effectiveness < 3.0 and problems:
            return f"Hi {name}! Yesterday was challenging with {problems}. Let's start today with a fresh approach. What would you like to focus on first?"

    # Priority 3: Regular time-based greetings
    if 6 <= current_hour < 12:
        return f"Good morning, {name}! Ready to tackle your learning goals today? What's your main focus?"
    elif 12 <= current_hour < 18:
        return f"Good afternoon, {name}! How's your learning progress going today?"
    elif 18 <= current_hour < 22:
        return f"Good evening, {name}! Time to reflect on today's learning. What went well?"

    return None


# --- SCRIPT EXECUTION ---

if __name__ == "__main__":
    knowledge_collection = initialize_system()
    main_loop(knowledge_collection)
